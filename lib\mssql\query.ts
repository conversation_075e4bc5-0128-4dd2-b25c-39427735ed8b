"server-only";

import { GetPrice4Lvl, GetPrice4LvlBatch, PretSiStocAM, PretSiStocAMWithItemNo } from "@/types/mssql";
import { categoryIdSchemaMSSQL, productCodSchema } from "../zod";
import { mssql } from "./mssql";
import { logger } from "../logger";

export const getPretSiStoc = async (itemno: string): Promise<PretSiStocAM[]> => {
	try {
		if (!itemno?.trim()) {  
			logger.warn("[getPretSiStoc]Invalid parameters:", { itemno });
			return [];
		}

		const validated = categoryIdSchemaMSSQL.safeParse( itemno );

		if(!validated.success){
			logger.error("[getPretSiStoc]Invalid parameters with zod:", { itemno });
			return [];
		}

		const validatedItemno = validated.data;
	  
		const query = `
			SELECT
				ISNULL(st._UNITID, '') AS locatie,
				ISNULL(st.stockfig, 0) - ISNULL(si.Num, 0) - ISNULL(srr.Transfer, 0) AS stoc,
				CAST((i.selpr) AS DECIMAL(16, 2)) AS pret
				FROM ALL_ITEM i
				LEFT JOIN ALL_stit st ON i.ITEMNO = st.ITEMNO AND i.SUPLNO = st.SUPLNO AND st._UNITID in ( 'BAN', 'OTP', 'ACJ', 'MIL', 'BCT', 'ABC', 'CSB', 'BAR', 'BTM', 'CBV', 'CRA', 'JIL', 'MTG')
				LEFT JOIN (SELECT a.ITEMNO, b._UNITID, SUM(a.num) 'Num'
					FROM ALL_grow a
					LEFT JOIN ALL_gsal b ON a.gsalid = b.gsalid AND a._UNITID = b._UNITID
					WHERE CAST(b.servd AS DATE) >= CAST(GETDATE() AS DATE)
						AND CAST(b.servd AS DATE) < CAST(GETDATE() + 30 AS DATE)
						AND b.stype = 'A'
						AND b.STATUS = 'A'
						AND a.SUPLNO = '1'
					GROUP BY a.ITEMNO, b._UNITID) si ON st.ITEMNO = si.ITEMNO AND st._UNITID = si._UNITID
				LEFT JOIN (SELECT sr.ITEMNO, sr.SUPLNO, SUM(sr.DNUM) 'Transfer'
					FROM ALL_STRH sh
					LEFT JOIN ALL_STRR sr ON sh.TRANSID = sr.TRANSID
					WHERE sh.TARGETUNITID in ('BAN', 'OTP', 'ACJ', 'MIL', 'BCT', 'ABC', 'CSB', 'BAR', 'BTM', 'CBV', 'CRA', 'JIL', 'MTG')
						AND sh.STATUS IN (2, 3)
					GROUP BY sr.ITEMNO, sr.SUPLNO) srr ON srr.ITEMNO = st.ITEMNO AND srr.SUPLNO = st.SUPLNO
				WHERE i.itemno = @itemno AND i.SUPLNO = 1
		`;

		const params = { itemno: validatedItemno };

		const result = await mssql.queryWithDeadlockRetry<PretSiStocAM>(query, params);

		if (!result?.length) {
			logger.warn(`[getPretSiStoc] No stock/price data found for Cod: ${itemno}`);
			return [];
		}

		const transformedResult = result.map(item => ({
			locatie: item.locatie.substring(0, 50),
			stoc: Math.max(0, item.stoc),
			pret: Number(item.pret.toFixed(2))
		}));

		return transformedResult;
  } catch (error) {
    logger.error(`[getPretSiStoc] Error fetching stock/price data: ${error}`);
    return [];
  }
};

// export const getPretSiStocBatch = async (itemnos: string[]): Promise<Record<string, PretSiStocAM[]>> => {
// 	if (itemnos.length === 0) return {}; // Handle empty case
  
// 	try {
// 	  // Ensure input values are valid
// 	  const validated = itemnos.map((itemno) => productCodSchema.safeParse(itemno));

// 	  if (validated.some((v) => !v.success)) {
// 		logger.error("[getPretSiStocBatch]Invalid parameters with zod:", { itemnos });
// 		return {};
// 	  }

// 	  const validatedItemnos = validated.map((v) => v.data);
  
// 	  // Construct a dynamic SQL query using WHERE IN
// 	  const query = `
// 		SELECT 
// 		  i.itemno,
// 		  ISNULL(st._UNITID, '') AS locatie,
// 		  ISNULL(st.stockfig, 0) - ISNULL(si.Num, 0) - ISNULL(srr.Transfer, 0) AS stoc,
// 		  CAST((i.selpr) AS DECIMAL(16, 2)) AS pret
// 		FROM ALL_ITEM i
// 		LEFT JOIN ALL_stit st ON i.ITEMNO = st.ITEMNO AND i.SUPLNO = st.SUPLNO 
// 		  AND st._UNITID IN ('BAN', 'OTP', 'ACJ', 'MIL', 'BCT', 'ABC', 'CSB', 'BAR', 'BTM', 'CBV', 'CRA', 'JIL', 'MTG')
// 		LEFT JOIN (
// 		  SELECT a.ITEMNO, b._UNITID, SUM(a.num) AS Num
// 		  FROM ALL_grow a
// 		  LEFT JOIN ALL_gsal b ON a.gsalid = b.gsalid AND a._UNITID = b._UNITID
// 		  WHERE CAST(b.servd AS DATE) >= CAST(GETDATE() AS DATE)
// 			AND CAST(b.servd AS DATE) < CAST(GETDATE() + 30 AS DATE)
// 			AND b.stype = 'A' AND b.STATUS = 'A' AND a.SUPLNO = '1'
// 		  GROUP BY a.ITEMNO, b._UNITID
// 		) si ON st.ITEMNO = si.ITEMNO AND st._UNITID = si._UNITID
// 		LEFT JOIN (
// 		  SELECT sr.ITEMNO, sr.SUPLNO, SUM(sr.DNUM) AS Transfer
// 		  FROM ALL_STRH sh
// 		  LEFT JOIN ALL_STRR sr ON sh.TRANSID = sr.TRANSID
// 		  WHERE sh.TARGETUNITID IN ('BAN', 'OTP', 'ACJ', 'MIL', 'BCT', 'ABC', 'CSB', 'BAR', 'BTM', 'CBV', 'CRA', 'JIL', 'MTG')
// 			AND sh.STATUS IN (2, 3)
// 		  GROUP BY sr.ITEMNO, sr.SUPLNO
// 		) srr ON srr.ITEMNO = st.ITEMNO AND srr.SUPLNO = st.SUPLNO
// 		WHERE i.itemno IN (${validatedItemnos.map((_, i) => `@item${i}`).join(", ")}) AND i.SUPLNO = 1
// 	  `;
  
// 	  // Generate query parameters
// 	  const params = validatedItemnos.reduce((acc, itemno, i) => {
// 		acc[`item${i}`] = itemno;
// 		return acc;
// 	  }, {} as { [key: string]: any });
  
// 	  //const result = await mssql.queryWithParams<PretSiStocAM>(query, params);
// 	  const result = await mssql.queryWithDeadlockRetry<PretSiStocAMWithItemNo>(query, params);

// 	  if (!result?.length) {
// 		logger.warn(`[getPretSiStocBatch] No stock/price data found for given items`);
// 		return {};
// 	  }
  
// 	  // Transform result into a lookup object
// 	  const transformedResult = result.reduce((acc, item) => {
// 		if (!acc[item.itemno]) acc[item.itemno] = [];
// 		acc[item.itemno].push({
// 		  locatie: item.locatie.substring(0, 50),
// 		  stoc: Math.max(0, item.stoc),
// 		  pret: Number(item.pret),
// 		});
// 		return acc;
// 	  }, {} as Record<string, PretSiStocAM[]>);
  
// 	  return transformedResult;
// 	} catch (error) {
// 	  logger.error(`[getPretSiStocBatch] Error fetching stock/price data: ${error}`);
// 	  return {};
// 	}
// };

export const getPretSiStocBatch = async (itemnos: string[]): Promise<Record<string, PretSiStocAM[]>> => {
	if (itemnos.length === 0) return {}; // Handle empty case
  
	try {
	  // Ensure input values are valid
	  const validated = itemnos.map((itemno) => productCodSchema.safeParse(itemno));

	  if (validated.some((v) => !v.success)) {
		logger.error("[getPretSiStocBatch]Invalid parameters with zod:", { itemnos });
		return {};
	  }

	  const validatedItemnos = validated.map((v) => v.data);
  
	  // Construct a dynamic SQL query using WHERE IN
		const query = `
			SELECT 
			i.itemno,
			ISNULL(st._UNITID, '') AS locatie,
			ISNULL(st.stockfig, 0) - ISNULL(si.Num, 0) - ISNULL(srr.Transfer, 0) AS stoc,
			CAST((i.selpr) AS DECIMAL(16, 2)) AS pret
			FROM ALL_ITEM i
			LEFT JOIN ALL_stit st ON i.ITEMNO = st.ITEMNO AND i.SUPLNO = st.SUPLNO 
			AND st._UNITID IN ('BAN', 'OTP', 'ACJ', 'MIL', 'BCT', 'ABC', 'CSB', 'BAR', 'BTM', 'CBV', 'CRA', 'JIL', 'MTG')
			LEFT JOIN (
			SELECT a.ITEMNO, b._UNITID, SUM(a.num) AS Num
			FROM ALL_grow a
			LEFT JOIN ALL_gsal b ON a.gsalid = b.gsalid AND a._UNITID = b._UNITID
			WHERE CAST(b.servd AS DATE) >= CAST(GETDATE() AS DATE)
				AND CAST(b.servd AS DATE) < CAST(GETDATE() + 30 AS DATE)
				AND b.stype = 'A' AND b.STATUS = 'A' AND a.SUPLNO = '1'
			GROUP BY a.ITEMNO, b._UNITID
			) si ON st.ITEMNO = si.ITEMNO AND st._UNITID = si._UNITID
			LEFT JOIN (
			SELECT sr.ITEMNO, sr.SUPLNO, SUM(sr.DNUM) AS Transfer
			FROM ALL_STRH sh
			LEFT JOIN ALL_STRR sr ON sh.TRANSID = sr.TRANSID
			WHERE sh.TARGETUNITID IN ('BAN', 'OTP', 'ACJ', 'MIL', 'BCT', 'ABC', 'CSB', 'BAR', 'BTM', 'CBV', 'CRA', 'JIL', 'MTG')
				AND sh.STATUS IN (2, 3)
			GROUP BY sr.ITEMNO, sr.SUPLNO
			) srr ON srr.ITEMNO = st.ITEMNO AND srr.SUPLNO = st.SUPLNO
			WHERE i.itemno IN (${validatedItemnos.map((_, i) => `@item${i}`).join(", ")}) AND i.SUPLNO = 1
		`;
  
	  // Generate query parameters with proper typing
	  const params = validatedItemnos.reduce<Record<string, string>>((acc, itemno, i) => {
		acc[`item${i}`] = itemno as string;
		return acc;
	  }, {});
  
	  const result = await mssql.queryWithDeadlockRetry<PretSiStocAMWithItemNo>(query, params);

	  if (!result?.length) {
		logger.warn(`[getPretSiStocBatch] No stock/price data found for given items`);
		return {};
	  }
  
	  // Transform result into a lookup object
	  const transformedResult = result.reduce<Record<string, PretSiStocAM[]>>((acc, item) => {
		if (!acc[item.itemno]) acc[item.itemno] = [];
		acc[item.itemno].push({
		  locatie: item.locatie.substring(0, 50),
		  stoc: Math.max(0, item.stoc),
		  pret: Number(item.pret),
		});
		return acc;
	  }, {});
  
	  return transformedResult;
	} catch (error) {
	  logger.error(`[getPretSiStocBatch] Error fetching stock/price data: ${error}`);
	  return {};
	}
};

export const getPriceFor4th = async (itemno: string, customerNo: string): Promise<GetPrice4Lvl | null> => {
	try {
		if (!itemno?.trim() || !customerNo?.trim()) {  
			logger.warn("[getPriceFor4th]Invalid parameters:", { itemno, customerNo });
			return null;
		}
  
		const validatedItemno = categoryIdSchemaMSSQL.safeParse(itemno );
		const validatedCustomerNo = categoryIdSchemaMSSQL.safeParse(customerNo );

		if(!validatedItemno.success || !validatedCustomerNo.success){
			logger.error("[getPriceFor4th]Invalid parameters:", { itemno, customerNo });
			return null;
		}

		const validatedItemnoData = validatedItemno.data;
		const validatedCustomerNoData = validatedCustomerNo.data;

		const query = `
			SELECT CASE 
				WHEN ISNULL(i.SELPR - i.SELPR * c.DISCPC, 0) = 0 
				THEN ISNULL(i.STRADEPR - i.STRADEPR * cc.DISCPC, 0)
				WHEN ISNULL(i.STRADEPR - i.STRADEPR * cc.DISCPC, 0) = 0 
				THEN i.SELPR - i.SELPR * c.DISCPC
				WHEN ISNULL(i.SELPR - i.SELPR * c.DISCPC, 0) < ISNULL(i.STRADEPR - i.STRADEPR * cc.DISCPC, 0) 
				THEN ISNULL(i.SELPR - i.SELPR * c.DISCPC, 0)
				ELSE ISNULL(i.STRADEPR - i.STRADEPR * cc.DISCPC, 0)
			END AS pret
			FROM ITEM i
			LEFT JOIN CUST cu ON cu.CUSTID = cu.CUSTID
			LEFT JOIN CUPP c ON i.ITEMNO = c.ITEMNO AND i.SUPLNO = c.SUPLNO AND c.CUSTID = cu.CUSTID
			LEFT JOIN (
				SELECT cu.CUSTID, dt.DISCPOS, dt.DISCPC
				FROM CUCD cu
				LEFT JOIN DTPC dt ON cu.DDTBLID = dt.DTBLID
			) cc ON cu.CUSTID = cc.CUSTID AND i.DISCGRP = cc.DISCPOS
			WHERE i.ITEMNO = @itemno 
			AND i.SUPLNO = 1 
			AND cu.CUSTID = @customerNo
			AND ISNULL(i.SELPR - i.SELPR * c.DISCPC, 0) + ISNULL(i.STRADEPR - i.STRADEPR * cc.DISCPC, 0) > 0
		`;

		const params = { itemno: validatedItemnoData, customerNo: validatedCustomerNoData };

		const result = await mssql.queryWithDeadlockRetry<GetPrice4Lvl>(query, params);

		if (!result?.length) {
			logger.warn(`[getPriceFor4th] No price data found for item: ${itemno}`);
			return null;
		}

		return { pret: result[0].pret !== null ? Number(result[0].pret.toFixed(2)) : null };
	} catch (error) {
		logger.error(`[getPriceFor4th] Error fetching price data: ${error}`);
		return null;
	}
};
  
export const getPricesFor4thBatch = async (itemnos: string[], customerNo: string): Promise<GetPrice4LvlBatch[]> => {
	console.log("getPricesFor4thBatch called for", itemnos);
	try {
		if (!Array.isArray(itemnos) || itemnos.length === 0 || !customerNo?.trim()) {
			logger.warn("[getPricesFor4thBatch]Empty parameters:", { itemnos, customerNo });
			return []; // ✅ Always return an array
		  }

		const validatedItems = itemnos.map((itemno) => categoryIdSchemaMSSQL.safeParse(itemno));
		const validatedCustomerNo = categoryIdSchemaMSSQL.safeParse(customerNo );

		if(validatedItems.some((v) => !v.success) || !validatedCustomerNo.success){
			logger.error("[getPricesFor4thBatch]Invalid parameters:", { itemnos, customerNo });
			return []; // ✅ Always return an array
		}

		const validatedItemsData = validatedItems.map((v) => v.data);
		const validatedCustomerNoData = validatedCustomerNo.data;

		// 🔹 Generate dynamic placeholders for SQL parameters
		const query = `
			SELECT i.ITEMNO AS itemno, 
				CASE 
				WHEN ISNULL(i.SELPR - i.SELPR * c.DISCPC, 0) = 0 
					THEN ISNULL(i.STRADEPR - i.STRADEPR * cc.DISCPC, 0)
				WHEN ISNULL(i.STRADEPR - i.STRADEPR * cc.DISCPC, 0) = 0 
					THEN i.SELPR - i.SELPR * c.DISCPC
				WHEN ISNULL(i.SELPR - i.SELPR * c.DISCPC, 0) < ISNULL(i.STRADEPR - i.STRADEPR * cc.DISCPC, 0) 
					THEN ISNULL(i.SELPR - i.SELPR * c.DISCPC, 0)
				ELSE ISNULL(i.STRADEPR - i.STRADEPR * cc.DISCPC, 0)
				END AS pret
			FROM ITEM i
			LEFT JOIN CUST cu ON cu.CUSTID = cu.CUSTID
			LEFT JOIN CUPP c ON i.ITEMNO = c.ITEMNO AND i.SUPLNO = c.SUPLNO AND c.CUSTID = cu.CUSTID
			LEFT JOIN (
				SELECT cu.CUSTID, dt.DISCPOS, dt.DISCPC
				FROM CUCD cu
				LEFT JOIN DTPC dt ON cu.DDTBLID = dt.DTBLID
			) cc ON cu.CUSTID = cc.CUSTID AND i.DISCGRP = cc.DISCPOS
			WHERE 
				i.ITEMNO IN (${validatedItemsData.map((_, i) => `@item${i}`).join(", ")})
				AND i.SUPLNO = 1
				AND cu.CUSTID = @customerNo
				AND ISNULL(i.SELPR - i.SELPR * c.DISCPC, 0) + ISNULL(i.STRADEPR - i.STRADEPR * cc.DISCPC, 0) > 0
		`;

		// 🔹 Prepare dynamic parameters
		const params = {
			customerNo: validatedCustomerNoData,
			...validatedItemsData.reduce((acc, itemno, i) => ({ ...acc, [`item${i}`]: itemno }), {})
		};

		const result = await mssql.queryWithDeadlockRetry<GetPrice4LvlBatch>(query, params);

		if (!result || result.length === 0) {
			logger.warn(`[getPricesFor4thBatch]No price data found for given items`);			
			return [];
		}

		return result.map((item) => ({
			itemno: item.itemno, // Ensure this matches your SQL column names
			pret: Number(item.pret.toFixed(2))
		}));
	} catch (error) {
		logger.error(`[getPricesFor4thBatch] Error fetching price data: ${error}`);
		return [];
	}
};
  
