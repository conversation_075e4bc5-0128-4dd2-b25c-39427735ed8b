"server-only"

import { prisma, withRetry } from "@/lib/db"
import { logger } from "@/lib/logger";

export interface HeroBanner {
  id: string;
  title: string;
  subtitle?: string | null;
  imageUrl: string;
  mobileImageUrl: string | null;
  callToAction: string | null;
  buttonText: string | null;
  description: string | null;
  url: string | null;
  position: number;
  textAlignment: string | null;
}

//get the banners with HERO placement from prisma
export async function getHeroBanners(): Promise<HeroBanner[]> {
  try{
    const banners = await withRetry(() => prisma.banner.findMany({
      where: {
        placement: "HERO",
        isActive: true
      },
      select: {
        id: true,
        title: true,
        subtitle: true,
        imageUrl: true,
        mobileImageUrl: true,
        callToAction: true,
        buttonText: true,
        description: true,
        url: true,
        position: true,
        textAlignment: true
      },
      orderBy: {
        position: "asc"
      }
    }))

    if(banners.length === 0){
      logger.warn('[getHeroBanners] No Hero banners to return')
      return []
    }  

    return banners
  }catch(e){
    logger.error(`[getHeroBanners] Error trying to get the Hero Banners: ${e}`)
    return []
  }
}

//get the banners with CATEGORY_SECTION_LANDING_PAGE placement from prisma
export async function getCategorySectionLandingPageBanners(): Promise<HeroBanner[]> {
  try{
    const banners = await withRetry(() => prisma.banner.findMany({
      where: {
        placement: "CATEGORY_SECTION_LANDING_PAGE",
        isActive: true
      },
      select: {
        id: true,
        title: true,
        subtitle: true,
        imageUrl: true,
        mobileImageUrl: true,
        callToAction: true,
        buttonText: true,
        description: true,
        url: true,
        position: true,
        textAlignment: true
      },
      orderBy: {
        position: "asc"
      }
    }))

    if(banners.length === 0){
      logger.warn('[getCategorySectionLandingPageBanners] No CategorySection banners to return')
      return []
    }

    return banners
  }catch(e){
            logger.error(`[getCategorySectionLandingPageBanners] Error trying to get the CategorySection : ${e}`)
    return []
  }
}   