"use client";

import { updateCartActiondata } from "@/app/actions/cart";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { CartItem } from "@/types/cart";
import { Minus, Plus } from "lucide-react";
import Image from "next/image";
import { useState, useTransition } from "react";
import { toast } from "sonner";
import { useEffect } from "react";
import { CartButtonStergeRoute } from "./CartButtonStergeRoute";
import Link from "next/link";

export default function CartPageItem({ item }: { item: CartItem }) {
  const [isPending, startTransition] = useTransition();
  const [currentItem, setCurrentItem] = useState(item);


  // Local input states
  const [localVinNotes, setLocalVinNotes] = useState(currentItem.vinNotes || '');
  const [localQuantity, setLocalQuantity] = useState(currentItem.quantity);

  // Sync when item changes
  useEffect(() => {
    setLocalVinNotes(currentItem.vinNotes || '');
    setLocalQuantity(currentItem.quantity);
  }, [currentItem]);

  // Handle enter key for quantity
  const handleQuantityKey = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleUpdate({ quantity: Number(localQuantity) });
    }
  };

  // Handle enter key for vinNotes
  const handleVinNotesKey = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleUpdate({ vinNotes: localVinNotes || null });
    }
  };

  const handleUpdate = async (updates: Partial<CartItem>) => {
    const previous = { ...currentItem };
    const updated = { ...currentItem, ...updates };

    setCurrentItem(updated); // Optimistically update UI

    startTransition(async () => {
      try {
        const response = await updateCartActiondata({ itemId: item.id, ...updates });

        if (response?.success) {
            if (updates.quantity === 0) {
            toast.success("Sters cu succes din cos.");
          } else {
            toast.success("Produsul a fost actualizat.");
          }
        } else {
          toast.error("Eșec la actualizare.");
        }
      } catch {
        setCurrentItem(previous); // Revert on error
        toast.error("Nu s-a putut actualiza produsul.");
      }
    });
  };

  return (
    <>
      <Checkbox
        checked={currentItem.addToOrder}
        onCheckedChange={(checked) => handleUpdate({ addToOrder: !!checked })}
        disabled={isPending}
      />

      <div className="w-24 h-24 rounded-lg overflow-hidden">
        <Link href={`/product/${currentItem.Material_Number}`}>
          <Image
            width={100}
            height={100}
            src={currentItem.ImageUrl}
            alt={currentItem.Description_Local || ""}
            className="w-full h-full object-cover"
          />
        </Link>
      </div>

      <div className="flex-1">
        <Link href={`/product/${currentItem.Material_Number}`}>
          <h3 className="text-lg font-medium">{currentItem.Description_Local}</h3>
          <p className="text-sm text-gray-500 mt-1">OE: {currentItem.Material_Number}</p>
        </Link>

        <div className="flex items-center mt-2 gap-6">
            <Input
                value={localVinNotes}
                onChange={(e) => setLocalVinNotes(e.target.value)}
                onBlur={() => handleUpdate({ vinNotes: localVinNotes })}
                onKeyDown={handleVinNotesKey}
                id={`obs-${item.id}`}
                placeholder="Observații sau VIN"
                className="border-gray-300 focus:ring-1 focus:ring-gray-400"
                disabled={isPending}
            />            

          <div className="flex items-center space-x-2">
            <Switch
              disabled={isPending}
              id={`switch-${item.id}`}
              checked={currentItem.addVinNotesToInvoice}
              onCheckedChange={(checked) => handleUpdate({ addVinNotesToInvoice: checked })}
            />
            <Label htmlFor={`switch-${item.id}`} className="text-xs text-gray-500">
              Adaugă pe factură
            </Label>
          </div>
        </div>

        <div className="flex items-center gap-4 mt-4">
          <div className="flex items-center gap-2">
            <Button
              disabled={isPending}
              variant="outline"
              size="icon"
              className="h-8 w-8"
              onClick={() => handleUpdate({ quantity: Math.max(1, currentItem.quantity - 1) })}
            >
              <Minus className="w-4 h-4" />
            </Button>

            <Input
                value={localQuantity}
                onChange={(e) => setLocalQuantity(Number(e.target.value))}
                onBlur={() => handleUpdate({ quantity: Number(localQuantity) })}
                onKeyDown={handleQuantityKey}
                className="w-16 h-8 text-center"
                min="1"
                type="number"
                disabled={isPending}
            />

            <Button
              disabled={isPending}
              variant="outline"
              size="icon"
              className="h-8 w-8"
              onClick={() => handleUpdate({ quantity: currentItem.quantity + 1 })}
            >
              <Plus className="w-4 h-4" />
            </Button>
          </div>

          <CartButtonStergeRoute item={item} />
        </div>
      </div>
    </>
  );
}



// "use client";

// import { updateCartActiondata } from "@/app/actions/cart";
// import { Button } from "@/components/ui/button";
// import { Checkbox } from "@/components/ui/checkbox";
// import { Input } from "@/components/ui/input";
// import { Label } from "@/components/ui/label";
// import { Switch } from "@/components/ui/switch";
// import { CartItem } from "@/types/cart";
// import { Minus, Plus, Trash } from "lucide-react";
// import Image from "next/image";
// import { useState, useTransition } from "react";
// import { toast } from "sonner";

// export default function CartPageItem({ item }: { item: CartItem }) {
//     const [isPending, startTransition] = useTransition();
//     const [currentItem, setCurrentItem] = useState(item);

//     const handleUpdate = async (updates: Partial<typeof item>) => {
//         setCurrentItem(prev => ({ ...prev, ...updates }));
//         await updateCartActiondata({ itemId: item.id, ...updates });
//     };

//   return ( 
//     <>               
//         <Checkbox
//             checked={currentItem.addToOrder}
//             onCheckedChange={(checked) => handleUpdate({ addToOrder: !!checked })}
//             disabled={isPending}
//         />
//         <div className="w-24 h-24 rounded-lg overflow-hidden">
//             <Image
//                 width={100}
//                 height={100}
//                 src={item.ImageUrl}
//                 alt={item.Description_Local}
//                 className="w-full h-full object-cover"
//             />
//         </div>

//         <div className="flex-1">
//             <h3 className="text-lg font-medium">{item.Description_Local}</h3>
//             <p className="text-sm text-gray-500 mt-1">
//                 OE: {item.Material_Number}
//             </p>
//             <div className="flex items-center mt-2 gap-6">
//                 <Input
//                     value={currentItem.vinNotes || ''}
//                     onChange={(e) => handleUpdate({ vinNotes: e.target.value })}
//                     id={`obs-${item.id}`}
//                     placeholder="Observatii sau VIN"
//                     className=" border-gray-300 focus:ring-1 focus:ring-gray-400"
//                     disabled={isPending}
//                 />
//                 <div className="flex items-center space-x-2">
//                     <Switch
//                         disabled={isPending}
//                         id="adauga-pe-factura"
//                         checked={currentItem.addVinNotesToInvoice}
//                         onCheckedChange={(checked) => handleUpdate({ addVinNotesToInvoice: checked })}
//                     />
//                     <Label htmlFor="adauga-pe-factura" className="text-xs text-gray-500">Adauga pe factura</Label>
//                 </div>
//             </div>
//             <div className="flex items-center gap-4 mt-4">
//                 <div className="flex items-center gap-2">
//                     <Button disabled={isPending} variant="outline" size="icon" className="h-8 w-8" onClick={() => { handleUpdate({ quantity: Math.max(1, currentItem.quantity - 1) })}}>
//                         <Minus className="w-4 h-4" />
//                     </Button>
//                     <Input
//                         value={currentItem.quantity}
//                         onChange={(e) => handleUpdate({ quantity: Number(e.target.value) })}
//                         className="w-16 h-8 text-center"
//                         min="1"
//                         disabled={isPending}
//                       />
//                     <Button disabled={isPending} variant="outline" size="icon" className="h-8 w-8" onClick={() => handleUpdate({ quantity: currentItem.quantity + 1 })}>
//                         <Plus className="w-4 h-4" />
//                     </Button>
//                 </div>
//                 <Button
//                     variant="outline"
//                     size="sm"
//                     className="gap-2 text-red-600 hover:text-red-700"
//                     onClick={() => handleUpdate({ quantity: 0 })}
//                     disabled={isPending}
//                 >
//                     <Trash className="w-4 h-4" /> {isPending ? 'Se șterge...' : 'Sterge'}
//                 </Button>
//             </div>
//         </div>
//     </>
//   )}