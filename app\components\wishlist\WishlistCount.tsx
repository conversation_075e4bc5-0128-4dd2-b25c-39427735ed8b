"use client"

import { Bad<PERSON> } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Heart } from "lucide-react";
import Link from "next/link";
import { useWishlist } from '@/app/context/WishlistContext'; // <-- THE CRUCIAL IMPORT

export function WishlistComponent() {
    const { getWishlistCount } = useWishlist();
    const wishlistCount = getWishlistCount();
  return (
    <div className="relative group">
      <Link href="/account/wishlist">
        <Button
          variant="ghost"
          size="icon"
          className="relative hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <Heart className="h-6 w-6 text-[#4D4D4D] dark:text-gray-300" />
            <Badge
              className="absolute -top-2 -right-2 bg-[#0066B1]"
              variant="secondary"
            >
              {wishlistCount}
            </Badge>
        </Button>
      </Link>
    </div>
  );
}
