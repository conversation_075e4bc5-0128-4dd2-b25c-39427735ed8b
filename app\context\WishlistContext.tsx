"use client";

import { createContext, useContext, useState, ReactNode, useOptimistic } from "react";

interface WishlistContextType {
  wishlistItems: Set<string>;
  optimisticWishlistItems: Set<string>;
  addToWishlistOptimistic: (itemId: string) => void;
  removeFromWishlistOptimistic: (itemId: string) => void;
  revertOptimisticUpdate: (itemId: string, shouldBeInWishlist: boolean) => void;
  updateActualWishlist: (itemId: string, isAdding: boolean) => void;
  isInWishlist: (itemId: string) => boolean;
  getWishlistCount: () => number;
}

const WishlistContext = createContext<WishlistContextType | undefined>(undefined);

export function WishlistProvider({
  children,
  initialItems = new Set<string>()
}: {
  children: ReactNode;
  initialItems?: Set<string>;
}) {
  const [wishlistItems, setWishlistItems] = useState<Set<string>>(initialItems);

  const [optimisticWishlistItems, setOptimisticWishlistItems] = useOptimistic(
    wishlistItems,
    (currentItems, action: { type: 'add' | 'remove' | 'revert', itemId: string, revertTo?: Set<string> }) => {
      const newItems = new Set(currentItems);

      switch (action.type) {
        case 'add':
          newItems.add(action.itemId);
          break;
        case 'remove':
          newItems.delete(action.itemId);
          break;
        case 'revert':
          return action.revertTo || currentItems;
      }

      return newItems;
    }
  );

  const addToWishlistOptimistic = (itemId: string) => {
    setOptimisticWishlistItems({ type: 'add', itemId });
  };

  const removeFromWishlistOptimistic = (itemId: string) => {
    setOptimisticWishlistItems({ type: 'remove', itemId });
  };

  const revertOptimisticUpdate = (itemId: string, shouldBeInWishlist: boolean) => {
    const revertedItems = new Set(wishlistItems);
    if (shouldBeInWishlist) {
      revertedItems.add(itemId);
    } else {
      revertedItems.delete(itemId);
    }
    setOptimisticWishlistItems({ type: 'revert', itemId, revertTo: revertedItems });
  };

  const isInWishlist = (itemId: string) => {
    return optimisticWishlistItems.has(itemId);
  };

  const getWishlistCount = () => {
    return optimisticWishlistItems.size;
  };

  // Update the actual wishlist items when server actions succeed
  const updateActualWishlist = (itemId: string, isAdding: boolean) => {
    setWishlistItems(prev => {
      const newItems = new Set(prev);
      if (isAdding) {
        newItems.add(itemId);
      } else {
        newItems.delete(itemId);
      }
      return newItems;
    });
  };

  const contextValue: WishlistContextType = {
    wishlistItems,
    optimisticWishlistItems,
    addToWishlistOptimistic,
    removeFromWishlistOptimistic,
    revertOptimisticUpdate,
    updateActualWishlist,
    isInWishlist,
    getWishlistCount
  };

  return (
    <WishlistContext.Provider value={contextValue}>
      {children}
    </WishlistContext.Provider>
  );
}

export function useWishlist() {
  const context = useContext(WishlistContext);
  if (context === undefined) {
    throw new Error('useWishlist must be used within a WishlistProvider');
  }
  return context;
}