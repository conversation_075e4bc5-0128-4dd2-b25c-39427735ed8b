
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.11.1
 * Query Engine version: f40f79ec31188888a2e33acda0ecc8fd10a853a9
 */
Prisma.prismaVersion = {
  client: "6.11.1",
  engine: "f40f79ec31188888a2e33acda0ecc8fd10a853a9"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  emailVerified: 'emailVerified',
  firstName: 'firstName',
  lastName: 'lastName',
  profileImage: 'profileImage',
  userAM: 'userAM',
  phoneNumber: 'phoneNumber',
  phoneVerified: 'phoneVerified',
  newsletterOptIn: 'newsletterOptIn',
  externalId: 'externalId',
  externalProvider: 'externalProvider',
  salutation: 'salutation',
  role: 'role',
  jobTitle: 'jobTitle',
  department: 'department',
  bio: 'bio',
  preferredLanguage: 'preferredLanguage',
  timezone: 'timezone',
  permissions: 'permissions',
  lastLoginAt: 'lastLoginAt',
  loginCount: 'loginCount',
  lastActivityAt: 'lastActivityAt',
  isActive: 'isActive',
  inactiveBy: 'inactiveBy',
  inactiveAt: 'inactiveAt',
  inactiveReason: 'inactiveReason',
  isSuspended: 'isSuspended',
  suspendedBy: 'suspendedBy',
  suspendedAt: 'suspendedAt',
  suspensionReason: 'suspensionReason',
  deletedAt: 'deletedAt',
  deletedBy: 'deletedBy',
  deletedReason: 'deletedReason',
  passwordEnabled: 'passwordEnabled',
  twoFactorEnabled: 'twoFactorEnabled',
  totpEnabled: 'totpEnabled',
  mfaEnabledAt: 'mfaEnabledAt',
  mfaDisabledAt: 'mfaDisabledAt',
  loginAttempts: 'loginAttempts',
  lockoutUntil: 'lockoutUntil',
  emailNotifications: 'emailNotifications',
  pushNotifications: 'pushNotifications',
  smsNotifications: 'smsNotifications',
  legal_accepted_at: 'legal_accepted_at',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy'
};

exports.Prisma.UserSessionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  sessionToken: 'sessionToken',
  expiresAt: 'expiresAt',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  deviceId: 'deviceId',
  location: 'location',
  lastActiveAt: 'lastActiveAt',
  isRevoked: 'isRevoked',
  revokedReason: 'revokedReason',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserGroupScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  permissions: 'permissions',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy'
};

exports.Prisma.UserNotificationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  title: 'title',
  message: 'message',
  type: 'type',
  isRead: 'isRead',
  readAt: 'readAt',
  link: 'link',
  createdAt: 'createdAt'
};

exports.Prisma.UserAuditLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  action: 'action',
  entityType: 'entityType',
  entityId: 'entityId',
  details: 'details',
  detailsJson: 'detailsJson',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  performedBy: 'performedBy',
  createdAt: 'createdAt'
};

exports.Prisma.OrderScalarFieldEnum = {
  id: 'id',
  orderNumber: 'orderNumber',
  amount: 'amount',
  isPaid: 'isPaid',
  vin: 'vin',
  invoiceAM: 'invoiceAM',
  updatesEnabled: 'updatesEnabled',
  terms: 'terms',
  orderStatus: 'orderStatus',
  paymentStatus: 'paymentStatus',
  paymentMethod: 'paymentMethod',
  shippingMethod: 'shippingMethod',
  shipmentStatus: 'shipmentStatus',
  showroom: 'showroom',
  placedAt: 'placedAt',
  processedAt: 'processedAt',
  completedAt: 'completedAt',
  cancelledAt: 'cancelledAt',
  shippingProcessedAt: 'shippingProcessedAt',
  shippedAt: 'shippedAt',
  deliveredAt: 'deliveredAt',
  paidAt: 'paidAt',
  refundedAt: 'refundedAt',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy',
  version: 'version',
  isActive: 'isActive',
  deletedAt: 'deletedAt',
  archivedAt: 'archivedAt',
  groupId: 'groupId',
  notes: 'notes',
  billingAddressId: 'billingAddressId',
  shippingAddressId: 'shippingAddressId',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  hasReturns: 'hasReturns',
  hasServiceRequests: 'hasServiceRequests'
};

exports.Prisma.OrderItemScalarFieldEnum = {
  id: 'id',
  quantity: 'quantity',
  price: 'price',
  notes: 'notes',
  notesToInvoice: 'notesToInvoice',
  vinOrderItem: 'vinOrderItem',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy',
  version: 'version',
  orderId: 'orderId',
  productId: 'productId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WishlistScalarFieldEnum = {
  id: 'id',
  productCode: 'productCode',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ShippingAddressScalarFieldEnum = {
  id: 'id',
  fullName: 'fullName',
  address: 'address',
  city: 'city',
  county: 'county',
  phoneNumber: 'phoneNumber',
  notes: 'notes',
  isDefault: 'isDefault',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BillingAddressScalarFieldEnum = {
  id: 'id',
  fullName: 'fullName',
  companyName: 'companyName',
  address: 'address',
  city: 'city',
  county: 'county',
  cui: 'cui',
  bank: 'bank',
  iban: 'iban',
  isDefault: 'isDefault',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BannerScalarFieldEnum = {
  id: 'id',
  title: 'title',
  subtitle: 'subtitle',
  imageUrl: 'imageUrl',
  mobileImageUrl: 'mobileImageUrl',
  callToAction: 'callToAction',
  buttonText: 'buttonText',
  description: 'description',
  url: 'url',
  placement: 'placement',
  position: 'position',
  width: 'width',
  height: 'height',
  backgroundColor: 'backgroundColor',
  textColor: 'textColor',
  textAlignment: 'textAlignment',
  targetAudience: 'targetAudience',
  deviceTarget: 'deviceTarget',
  startDate: 'startDate',
  endDate: 'endDate',
  isActive: 'isActive',
  impressions: 'impressions',
  clicks: 'clicks',
  conversionRate: 'conversionRate',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CategoryLevel1ScalarFieldEnum = {
  id: 'id',
  name: 'name',
  nameRO: 'nameRO',
  afisat: 'afisat',
  imageUrl: 'imageUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CategoryLevel2ScalarFieldEnum = {
  id: 'id',
  name: 'name',
  nameRO: 'nameRO',
  afisat: 'afisat',
  imageUrl: 'imageUrl',
  level1Id: 'level1Id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CategoryLevel3ScalarFieldEnum = {
  id: 'id',
  name: 'name',
  nameRO: 'nameRO',
  descriere: 'descriere',
  afisat: 'afisat',
  familyCode: 'familyCode',
  imageUrl: 'imageUrl',
  slug: 'slug',
  metaTitle: 'metaTitle',
  metaDescription: 'metaDescription',
  displayOrder: 'displayOrder',
  productCount: 'productCount',
  lastProductAdded: 'lastProductAdded',
  isActive: 'isActive',
  deletedAt: 'deletedAt',
  level2Id: 'level2Id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BrandScalarFieldEnum = {
  id: 'id',
  name: 'name',
  nameRO: 'nameRO',
  afisat: 'afisat',
  imageUrl: 'imageUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.VehicleModelScalarFieldEnum = {
  id: 'id',
  name: 'name',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductClassScalarFieldEnum = {
  id: 'id',
  classCode: 'classCode',
  brandId: 'brandId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductClassVehicleModelScalarFieldEnum = {
  productClassId: 'productClassId',
  vehicleModelId: 'vehicleModelId',
  createdAt: 'createdAt'
};

exports.Prisma.ProductScalarFieldEnum = {
  id: 'id',
  Material_Number: 'Material_Number',
  Net_Weight: 'Net_Weight',
  Description_Local: 'Description_Local',
  Base_Unit_Of_Measur: 'Base_Unit_Of_Measur',
  Cross_Plant: 'Cross_Plant',
  New_Material: 'New_Material',
  PretAM: 'PretAM',
  FinalPrice: 'FinalPrice',
  HasDiscount: 'HasDiscount',
  activeDiscountType: 'activeDiscountType',
  activeDiscountValue: 'activeDiscountValue',
  discountPercentage: 'discountPercentage',
  priceRange: 'priceRange',
  ImageUrl: 'ImageUrl',
  IsOnLandingPage: 'IsOnLandingPage',
  Material_Number_normalized: 'Material_Number_normalized',
  Description_Local_normalized: 'Description_Local_normalized',
  stockStatus: 'stockStatus',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy',
  version: 'version',
  isActive: 'isActive',
  deletedAt: 'deletedAt',
  Parts_Class: 'Parts_Class',
  classId: 'classId',
  Material_Group: 'Material_Group',
  categoryLevel3Id: 'categoryLevel3Id',
  isServiceable: 'isServiceable',
  warrantyMonths: 'warrantyMonths',
  createdAt: 'createdAt',
  last_updated_at: 'last_updated_at'
};

exports.Prisma.PriceHistoryScalarFieldEnum = {
  id: 'id',
  productId: 'productId',
  oldPretAM: 'oldPretAM',
  newPretAM: 'newPretAM',
  oldFinalPrice: 'oldFinalPrice',
  newFinalPrice: 'newFinalPrice',
  reason: 'reason',
  source: 'source',
  createdBy: 'createdBy',
  createdAt: 'createdAt'
};

exports.Prisma.ProductHistoryScalarFieldEnum = {
  id: 'id',
  Material_Number: 'Material_Number',
  changes: 'changes',
  snapshot: 'snapshot',
  change_type: 'change_type',
  version: 'version',
  changed_by: 'changed_by',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DiscountScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  type: 'type',
  value: 'value',
  startDate: 'startDate',
  endDate: 'endDate',
  active: 'active',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductDiscountScalarFieldEnum = {
  id: 'id',
  productId: 'productId',
  discountId: 'discountId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DiscountHistoryScalarFieldEnum = {
  id: 'id',
  discountId: 'discountId',
  changes: 'changes',
  snapshot: 'snapshot',
  change_type: 'change_type',
  version: 'version',
  changed_by: 'changed_by',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductAttributeScalarFieldEnum = {
  id: 'id',
  Material_Number: 'Material_Number',
  key: 'key',
  value: 'value',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductAttributeHistoryScalarFieldEnum = {
  id: 'id',
  Material_Number: 'Material_Number',
  changes: 'changes',
  snapshot: 'snapshot',
  change_type: 'change_type',
  version: 'version',
  changed_by: 'changed_by',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrderStatusHistoryScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  orderStatus: 'orderStatus',
  paymentStatus: 'paymentStatus',
  shipmentStatus: 'shipmentStatus',
  previousOrderStatus: 'previousOrderStatus',
  previousPaymentStatus: 'previousPaymentStatus',
  previousShipmentStatus: 'previousShipmentStatus',
  reason: 'reason',
  notes: 'notes',
  changedBy: 'changedBy',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  createdAt: 'createdAt'
};

exports.Prisma.ReturnScalarFieldEnum = {
  id: 'id',
  returnNumber: 'returnNumber',
  orderId: 'orderId',
  status: 'status',
  reason: 'reason',
  additionalNotes: 'additionalNotes',
  isApproved: 'isApproved',
  approvedBy: 'approvedBy',
  approvedAt: 'approvedAt',
  rejectionReason: 'rejectionReason',
  refundAmount: 'refundAmount',
  refundMethod: 'refundMethod',
  refundedAt: 'refundedAt',
  refundReference: 'refundReference',
  returnShippingLabel: 'returnShippingLabel',
  receivedAt: 'receivedAt',
  inspectedAt: 'inspectedAt',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ReturnItemScalarFieldEnum = {
  id: 'id',
  returnId: 'returnId',
  orderItemId: 'orderItemId',
  quantity: 'quantity',
  reason: 'reason',
  condition: 'condition',
  description: 'description',
  isReceived: 'isReceived',
  isInspected: 'isInspected',
  inspectionNotes: 'inspectionNotes',
  inspectionResult: 'inspectionResult',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ReturnStatusHistoryScalarFieldEnum = {
  id: 'id',
  returnId: 'returnId',
  previousStatus: 'previousStatus',
  newStatus: 'newStatus',
  notes: 'notes',
  changedBy: 'changedBy',
  createdAt: 'createdAt'
};

exports.Prisma.ServiceRequestScalarFieldEnum = {
  id: 'id',
  serviceNumber: 'serviceNumber',
  userId: 'userId',
  vin: 'vin',
  vehicleModel: 'vehicleModel',
  vehicleYear: 'vehicleYear',
  mileage: 'mileage',
  type: 'type',
  status: 'status',
  description: 'description',
  diagnosisNotes: 'diagnosisNotes',
  preferredDate: 'preferredDate',
  scheduledDate: 'scheduledDate',
  completedDate: 'completedDate',
  estimatedDuration: 'estimatedDuration',
  estimatedCost: 'estimatedCost',
  finalCost: 'finalCost',
  isPaid: 'isPaid',
  paidAt: 'paidAt',
  createdBy: 'createdBy',
  updatedBy: 'updatedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ServiceItemScalarFieldEnum = {
  id: 'id',
  serviceRequestId: 'serviceRequestId',
  productId: 'productId',
  description: 'description',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  totalPrice: 'totalPrice',
  itemType: 'itemType',
  laborHours: 'laborHours',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ServiceStatusHistoryScalarFieldEnum = {
  id: 'id',
  serviceRequestId: 'serviceRequestId',
  previousStatus: 'previousStatus',
  newStatus: 'newStatus',
  notes: 'notes',
  changedBy: 'changedBy',
  createdAt: 'createdAt'
};

exports.Prisma.CategorySectionScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  image: 'image',
  href: 'href'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.MisterMiss = exports.$Enums.MisterMiss = {
  Dl: 'Dl',
  Dna: 'Dna'
};

exports.Rol = exports.$Enums.Rol = {
  administAB: 'administAB',
  moderatorAB: 'moderatorAB',
  inregistratAB: 'inregistratAB',
  fourLvlAdminAB: 'fourLvlAdminAB',
  fourLvlInregistratAB: 'fourLvlInregistratAB',
  angajatAB: 'angajatAB'
};

exports.NotificationType = exports.$Enums.NotificationType = {
  INFO: 'INFO',
  SUCCESS: 'SUCCESS',
  WARNING: 'WARNING',
  ERROR: 'ERROR'
};

exports.OrderStatus = exports.$Enums.OrderStatus = {
  plasata: 'plasata',
  procesare: 'procesare',
  confirmata: 'confirmata',
  pregatita: 'pregatita',
  expediata: 'expediata',
  livrata: 'livrata',
  completa: 'completa',
  anulata: 'anulata',
  stornata: 'stornata',
  returnata: 'returnata',
  partiala: 'partiala'
};

exports.PaymentStatus = exports.$Enums.PaymentStatus = {
  asteptare: 'asteptare',
  succes: 'succes',
  esuat: 'esuat',
  rambursat: 'rambursat',
  partial_rambursat: 'partial_rambursat',
  contestat: 'contestat'
};

exports.PaymentMethod = exports.$Enums.PaymentMethod = {
  ramburs: 'ramburs',
  card: 'card',
  transfer: 'transfer',
  laTermen: 'laTermen'
};

exports.ShippingMethod = exports.$Enums.ShippingMethod = {
  curier: 'curier',
  showroom: 'showroom'
};

exports.ShipmentStatus = exports.$Enums.ShipmentStatus = {
  asteptare: 'asteptare',
  prelucrare: 'prelucrare',
  pregatit: 'pregatit',
  expediat: 'expediat',
  tranzit: 'tranzit',
  livrat: 'livrat',
  esuat: 'esuat',
  intors: 'intors',
  anulat: 'anulat',
  partial: 'partial'
};

exports.Showroom = exports.$Enums.Showroom = {
  CJ: 'CJ',
  BV: 'BV',
  TM: 'TM',
  AR: 'AR',
  BAC: 'BAC',
  BAN: 'BAN',
  OTP: 'OTP',
  MIL: 'MIL',
  TGM: 'TGM',
  JIL: 'JIL',
  CT: 'CT',
  CRA: 'CRA',
  SB: 'SB'
};

exports.BannerPlacement = exports.$Enums.BannerPlacement = {
  HOME: 'HOME',
  CATEGORY: 'CATEGORY',
  PRODUCT: 'PRODUCT',
  CHECKOUT: 'CHECKOUT',
  SIDEBAR: 'SIDEBAR',
  HEADER: 'HEADER',
  FOOTER: 'FOOTER',
  POPUP: 'POPUP',
  HERO: 'HERO',
  CATEGORY_SECTION_LANDING_PAGE: 'CATEGORY_SECTION_LANDING_PAGE'
};

exports.DeviceTarget = exports.$Enums.DeviceTarget = {
  ALL: 'ALL',
  DESKTOP: 'DESKTOP',
  MOBILE: 'MOBILE',
  TABLET: 'TABLET'
};

exports.DiscountType = exports.$Enums.DiscountType = {
  PERCENTAGE: 'PERCENTAGE',
  FIXED_AMOUNT: 'FIXED_AMOUNT',
  NEW_PRICE: 'NEW_PRICE'
};

exports.StockStatus = exports.$Enums.StockStatus = {
  IN_STOCK: 'IN_STOCK',
  LOW_STOCK: 'LOW_STOCK',
  OUT_OF_STOCK: 'OUT_OF_STOCK',
  DISCONTINUED: 'DISCONTINUED',
  UNKNOWN: 'UNKNOWN'
};

exports.ChangeType = exports.$Enums.ChangeType = {
  CRON_DISCOUNT_EXPIRED: 'CRON_DISCOUNT_EXPIRED',
  MANUAL_DISCOUNT_EXPIRED_BUTTON_PRESS: 'MANUAL_DISCOUNT_EXPIRED_BUTTON_PRESS',
  INSERT: 'INSERT',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  ORDER: 'ORDER',
  DISCOUNT: 'DISCOUNT',
  PRODUCT_ADDED: 'PRODUCT_ADDED',
  PRODUCT_DELETE_ALL: 'PRODUCT_DELETE_ALL',
  PRODUCT_DELETE: 'PRODUCT_DELETE',
  PRODUCT_ADD_TO_DISCOUNT_WITH_CSV: 'PRODUCT_ADD_TO_DISCOUNT_WITH_CSV',
  ADDED_TO_DISCOUNT: 'ADDED_TO_DISCOUNT',
  DELETED_FROM_DISCOUNT: 'DELETED_FROM_DISCOUNT'
};

exports.ReturnStatus = exports.$Enums.ReturnStatus = {
  requested: 'requested',
  approved: 'approved',
  rejected: 'rejected',
  awaitingReceipt: 'awaitingReceipt',
  received: 'received',
  inspected: 'inspected',
  refundIssued: 'refundIssued',
  completed: 'completed',
  cancelled: 'cancelled'
};

exports.ReturnReason = exports.$Enums.ReturnReason = {
  wrongItem: 'wrongItem',
  defective: 'defective',
  damaged: 'damaged',
  notAsDescribed: 'notAsDescribed',
  noLongerWanted: 'noLongerWanted',
  other: 'other'
};

exports.RefundMethod = exports.$Enums.RefundMethod = {
  originalPayment: 'originalPayment',
  storeCredit: 'storeCredit',
  bankTransfer: 'bankTransfer'
};

exports.ReturnItemReason = exports.$Enums.ReturnItemReason = {
  wrongItem: 'wrongItem',
  defective: 'defective',
  damaged: 'damaged',
  notAsDescribed: 'notAsDescribed',
  noLongerWanted: 'noLongerWanted',
  other: 'other'
};

exports.ItemCondition = exports.$Enums.ItemCondition = {
  asDescribed: 'asDescribed',
  damaged: 'damaged',
  opened: 'opened',
  used: 'used',
  missingParts: 'missingParts'
};

exports.InspectionResult = exports.$Enums.InspectionResult = {
  approved: 'approved',
  rejected: 'rejected',
  partiallyApproved: 'partiallyApproved'
};

exports.ServiceType = exports.$Enums.ServiceType = {
  repair: 'repair',
  maintenance: 'maintenance',
  warranty: 'warranty',
  installation: 'installation',
  inspection: 'inspection',
  other: 'other'
};

exports.ServiceStatus = exports.$Enums.ServiceStatus = {
  requested: 'requested',
  scheduled: 'scheduled',
  inProgress: 'inProgress',
  diagnosisComplete: 'diagnosisComplete',
  awaitingParts: 'awaitingParts',
  awaitingApproval: 'awaitingApproval',
  completed: 'completed',
  cancelled: 'cancelled',
  delivered: 'delivered'
};

exports.ServiceItemType = exports.$Enums.ServiceItemType = {
  part: 'part',
  labor: 'labor',
  fee: 'fee',
  other: 'other'
};

exports.Prisma.ModelName = {
  User: 'User',
  UserSession: 'UserSession',
  UserGroup: 'UserGroup',
  UserNotification: 'UserNotification',
  UserAuditLog: 'UserAuditLog',
  Order: 'Order',
  OrderItem: 'OrderItem',
  Wishlist: 'Wishlist',
  ShippingAddress: 'ShippingAddress',
  BillingAddress: 'BillingAddress',
  Banner: 'Banner',
  CategoryLevel1: 'CategoryLevel1',
  CategoryLevel2: 'CategoryLevel2',
  CategoryLevel3: 'CategoryLevel3',
  Brand: 'Brand',
  VehicleModel: 'VehicleModel',
  ProductClass: 'ProductClass',
  ProductClassVehicleModel: 'ProductClassVehicleModel',
  Product: 'Product',
  PriceHistory: 'PriceHistory',
  ProductHistory: 'ProductHistory',
  Discount: 'Discount',
  ProductDiscount: 'ProductDiscount',
  DiscountHistory: 'DiscountHistory',
  ProductAttribute: 'ProductAttribute',
  ProductAttributeHistory: 'ProductAttributeHistory',
  OrderStatusHistory: 'OrderStatusHistory',
  Return: 'Return',
  ReturnItem: 'ReturnItem',
  ReturnStatusHistory: 'ReturnStatusHistory',
  ServiceRequest: 'ServiceRequest',
  ServiceItem: 'ServiceItem',
  ServiceStatusHistory: 'ServiceStatusHistory',
  CategorySection: 'CategorySection'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
