"use client";

import { useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Loader2, Eye, EyeOff, Shield, Lock } from "lucide-react";
import { passwordChangeSchema, type PasswordChangeInput } from "@/lib/zod";
import { useUser, useReverification  } from "@clerk/nextjs";
import { toast } from 'sonner'; // Using a toast library like sonner is great for feedback
import { calculatePasswordStrength, getClerkErrorMessage, getStrengthText, getStrengthColor } from "@/lib/password";
import { ClerkAPIError } from '@clerk/types';

interface ClerkErrorResponse {
  errors: ClerkAPIError[];
}

export default function PasswordForm({ ssoProvider }: {ssoProvider?: string | null;}) {
  const { user, isLoaded } = useUser();
  const [isPending, startTransition] = useTransition();
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors, isDirty }
  } = useForm<PasswordChangeInput>({
    resolver: zodResolver(passwordChangeSchema),
    mode: "onBlur",
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    }
  });

  const newPassword = watch("newPassword");
  const passwordStrength = calculatePasswordStrength(newPassword || "");

  // const isClerkErrorResponse = (error: unknown): error is ClerkErrorResponse => {
  //   return (
  //     error !== null &&
  //     typeof error === 'object' &&
  //     'errors' in error &&
  //     Array.isArray((error as any).errors) &&
  //     (error as any).errors.length > 0 &&
  //     typeof (error as any).errors[0].code === 'string'
  //   );
  // };

  const isClerkErrorResponse = (error: unknown): error is ClerkErrorResponse => {
  if (
    typeof error !== 'object' ||
    error === null ||
    !('errors' in error)
  ) {
    return false;
  }

  const potentialError = error as { errors: unknown };

  if (!Array.isArray(potentialError.errors) || potentialError.errors.length === 0) {
    return false;
  }

  const firstError = potentialError.errors[0];

  return (
    typeof firstError === 'object' &&
    firstError !== null &&
    'code' in firstError &&
    typeof (firstError as { code: unknown }).code === 'string'
  );
};

  const changePass = useReverification(async (data: PasswordChangeInput) => {
      await user?.updatePassword({
      currentPassword: data.currentPassword,
      newPassword: data.newPassword,
      signOutOfOtherSessions: true,
    });
  })

  // Improved retry logic - no auto-retry
  const shouldAllowRetry = (error: unknown): boolean => {
    if (retryCount >= 3) return false;
    
    const retryableCodes = ['network_error', 'server_error'];
    
    if (isClerkErrorResponse(error) && error.errors.length > 0) {
      const firstError = error.errors[0];
      return retryableCodes.includes(firstError.code);
    }
    
    return false;
  };

  const onSubmit = (data: PasswordChangeInput) => {
    if (!isLoaded) {
      toast.loading("Se încarcă datele utilizatorului...", {
        duration: 3000
      });
      return;
    }

    if (!user) {
      toast.error("Eroare de autentificare", {
        description: "Utilizatorul nu este autentificat. Vă rugăm să vă reconectați.",
        duration: 4000
      });
      return;
    }

    if (passwordStrength.score < 80) {
      toast.warning("Parolă nesigură", {
        description: "Parola nu este suficient de sigură. Vă rugăm să urmați recomandările de mai jos.",
        duration: 4000
      });
      return;
    }

    startTransition(async () => {
      try {
        await changePass(data);

        toast.success("Succes!", {
          description: "Parola a fost schimbată cu succes!",
          duration: 3000
        });
        
        reset();
        setRetryCount(0);

      } catch (error) {

        const errorMessage = getClerkErrorMessage(error);
        const isRetryable = shouldAllowRetry(error);
        
        // Handle retryable vs non-retryable errors
        if (isRetryable && retryCount < 3) {
          toast.error("Eroare la schimbarea parolei", {
            description: `${errorMessage} Apăsați pentru a încerca din nou.`,
            duration: 5000,
            action: {
              label: `Încearcă din nou (${3 - retryCount})`,
              onClick: () => {
                setRetryCount(prev => prev + 1);
                handleSubmit(onSubmit)();
              }
            }
          });
          setRetryCount(prev => prev + 1);
        } else {
          toast.error("Eroare la schimbarea parolei", {
            description: errorMessage,
            duration: 4000
          });
        }
      }
    });
  };


  // If user signed up with SSO, show different UI
  if (ssoProvider) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lock className="h-5 w-5" />
            Schimbă Parola
          </CardTitle>
          <CardDescription>
            Parola este gestionată de furnizorul SSO
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-2">
                <p>
                  <strong>Parola este gestionată de {ssoProvider === 'microsoft' ? 'Microsoft' : ssoProvider === 'google' ? 'Google' : ssoProvider}.</strong>
                </p>
                <p className="text-sm text-muted-foreground">
                  Pentru a schimba parola, accesați setările contului dvs. {ssoProvider === 'microsoft' ? 'Microsoft' : ssoProvider === 'google' ? 'Google' : ssoProvider}.
                </p>
                <div className="mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const urls = {
                        google: 'https://myaccount.google.com/security',
                        microsoft: 'https://account.microsoft.com/security',
                        facebook: 'https://www.facebook.com/settings?tab=security',
                        github: 'https://github.com/settings/security',
                        apple: 'https://appleid.apple.com/account/manage'
                      };
                      window.open(urls[ssoProvider as keyof typeof urls] || '#', '_blank');
                    }}
                  >
                    Accesează setările {ssoProvider === 'microsoft' ? 'Microsoft' : ssoProvider === 'google' ? 'Google' : ssoProvider}
                  </Button>
                </div>
              </div>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  // If user signed up with password, show password change form
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Lock className="h-5 w-5" />
          Schimbă Parola
        </CardTitle>
        <CardDescription>
          Actualizați parola pentru a vă menține contul în siguranță
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Loading state indicator (no alert needed, toast handles errors) */}
          {!isLoaded && (
            <div className="flex items-center justify-center py-4">
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              <span className="text-sm text-muted-foreground">Se încarcă...</span>
            </div>
          )}

          {/* Current Password */}
          <div className="space-y-2">
            <Label htmlFor="currentPassword">Parola curentă *</Label>
            <div className="relative">
              <Input
                id="currentPassword"
                type={showCurrentPassword ? "text" : "password"}
                {...register("currentPassword")}
                className={errors.currentPassword ? "border-red-500 pr-10" : "pr-10"}
                placeholder="Introduceți parola curentă"
                disabled={isPending || !isLoaded}
                autoComplete="current-password"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                disabled={isPending}
                tabIndex={-1}
              >
                {showCurrentPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            {errors.currentPassword && (
              <p className="text-sm text-red-600">{errors.currentPassword.message}</p>
            )}
          </div>

          {/* New Password */}
          <div className="space-y-2">
            <Label htmlFor="newPassword">Parola nouă *</Label>
            <div className="relative">
              <Input
                id="newPassword"
                type={showNewPassword ? "text" : "password"}
                {...register("newPassword")}
                className={errors.newPassword ? "border-red-500 pr-10" : "pr-10"}
                placeholder="Introduceți parola nouă"
                disabled={isPending || !isLoaded}
                autoComplete="new-password"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowNewPassword(!showNewPassword)}
                disabled={isPending}
                tabIndex={-1}
              >
                {showNewPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            {errors.newPassword && (
              <p className="text-sm text-red-600">{errors.newPassword.message}</p>
            )}

            {/* Password Strength Indicator */}
            {newPassword && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Puterea parolei:</span>
                  <span className={`font-medium ${
                    passwordStrength.score < 40 ? 'text-red-600' :
                    passwordStrength.score < 60 ? 'text-orange-600' :
                    passwordStrength.score < 80 ? 'text-yellow-600' :
                    'text-green-600'
                  }`}>
                    {getStrengthText(passwordStrength.score)}
                  </span>
                </div>
                <Progress 
                  value={passwordStrength.score} 
                  className={`h-2 ${getStrengthColor(passwordStrength.score)}`} // You can use it directly
                />
                {passwordStrength.feedback.length > 0 && (
                  <div className="text-xs text-muted-foreground">
                    <p>Parola trebuie să conțină:</p>
                    <ul className="list-disc list-inside ml-2">
                      {passwordStrength.feedback.map((item, index) => (
                        <li key={index}>{item}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Confirm Password */}
          <div className="space-y-2">
            <Label htmlFor="confirmPassword">Confirmă parola nouă *</Label>
            <div className="relative">
              <Input
                id="confirmPassword"
                type={showConfirmPassword ? "text" : "password"}
                {...register("confirmPassword")}
                className={errors.confirmPassword ? "border-red-500 pr-10" : "pr-10"}
                placeholder="Confirmați parola nouă"
                disabled={isPending || !isLoaded}
                autoComplete="new-password"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                disabled={isPending}
                tabIndex={-1}
              >
                {showConfirmPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
            {errors.confirmPassword && (
              <p className="text-sm text-red-600">{errors.confirmPassword.message}</p>
            )}
          </div>

          {/* Security Tips */}
          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              <strong>Sfaturi pentru securitate:</strong>
              <ul className="list-disc list-inside mt-1 text-sm space-y-1">
                <li>Folosiți o parolă unică pentru acest cont</li>
                <li>Nu împărtășiți parola cu nimeni</li>
                <li>Schimbați parola regulat</li>
                <li>Activați autentificarea cu doi factori pentru securitate suplimentară</li>
              </ul>
            </AlertDescription>
          </Alert>

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button 
              type="submit" 
              disabled={isPending || !isDirty || passwordStrength.score < 80 || !isLoaded}
              className="bg-[#0066B1] hover:bg-[#004d85] text-white"
            >
              {isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Se schimbă...
                </>
              ) : (
                "Schimbă parola"
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}



// export default function PasswordForm({ lastPasswordChange, ssoProvider }: PasswordFormProps) {
//   const { user, isLoaded } = useUser();
//   const [isPending, startTransition] = useTransition();
//   //const [result, setResult] = useState<FormResult | null>(null);
//   const [showCurrentPassword, setShowCurrentPassword] = useState(false);
//   const [showNewPassword, setShowNewPassword] = useState(false);
//   const [showConfirmPassword, setShowConfirmPassword] = useState(false);
//   const [retryCount, setRetryCount] = useState(0);

//   const {
//     register,
//     handleSubmit,
//     watch,
//     reset,
//     formState: { errors, isDirty, isValid  }
//   } = useForm<PasswordChangeInput>({
//     resolver: zodResolver(passwordChangeSchema),
//     defaultValues: {
//       currentPassword: "",
//       newPassword: "",
//       confirmPassword: "",
//     }
//   });

//   // Password strength calculation
//   const calculatePasswordStrength = (password: string): { score: number; feedback: string[] } => {
//     const feedback: string[] = [];
//     let score = 0;

//     if (password.length >= 8) {
//       score += 20;
//     } else {
//       feedback.push("Cel puțin 8 caractere");
//     }

//     if (/[a-z]/.test(password)) {
//       score += 20;
//     } else {
//       feedback.push("O literă mică");
//     }

//     if (/[A-Z]/.test(password)) {
//       score += 20;
//     } else {
//       feedback.push("O literă mare");
//     }

//     if (/\d/.test(password)) {
//       score += 20;
//     } else {
//       feedback.push("O cifră");
//     }

//     if (/[@$!%*?&]/.test(password)) {
//       score += 20;
//     } else {
//       feedback.push("Un caracter special (@$!%*?&)");
//     }

//     return { score, feedback };
//   };

//   const newPassword = watch("newPassword");

//   const passwordStrength = calculatePasswordStrength(newPassword || "");

//     // Clear result after timeout
//   const clearResult = useCallback(() => {
//     const timer = setTimeout(() => setResult(null), 5000);
//     return () => clearTimeout(timer);
//   }, []);

//   // Retry logic for transient errors
//   const shouldRetry = (error: any): boolean => {
//     if (retryCount >= 3) return false;
    
//     const retryableCodes = ['network_error', 'server_error', 'rate_limit_exceeded'];
//     return error?.errors?.[0]?.code && retryableCodes.includes(error.errors[0].code);
//   };

//   const getStrengthColor = (score: number) => {
//     if (score < 40) return "bg-red-500";
//     if (score < 60) return "bg-orange-500";
//     if (score < 80) return "bg-yellow-500";
//     return "bg-green-500";
//   };

//   const getStrengthText = (score: number) => {
//     if (score < 40) return "Slabă";
//     if (score < 60) return "Medie";
//     if (score < 80) return "Bună";
//     return "Foarte bună";
//   };

//  const onSubmit = (data: PasswordChangeInput) => {

//       // Early validation
//     if (!isLoaded) {
//       setResult({ success: false, error: "Se încarcă datele utilizatorului..." });
//       return;
//     }

//     if (!user) {
//       setResult({ success: false, error: "Utilizatorul nu este autentificat. Vă rugăm să vă reconectați." });
//       return;
//     }

//         // Check password strength before submission
//     if (passwordStrength.score < 80) {
//       setResult({ success: false, error: "Parola nu este suficient de sigură. Vă rugăm să urmați recomandările de mai jos." });
//       return;
//     }

//     startTransition(async () => {
//       try {
//         // ✅ Single source of truth - Clerk handles everything
//         await user.updatePassword({
//           currentPassword: data.currentPassword,
//           newPassword: data.newPassword,
//           signOutOfOtherSessions: true
//         });


//         // Success handling
//         setResult({ 
//           success: true, 
//           timestamp: new Date() 
//         });
//         reset();
//         setRetryCount(0); // Reset retry counter
//         clearResult();

//       } catch (error: any) {
//         console.error('Password change error:', error);

//         // Increment retry count for retryable errors
//         if (shouldRetry(error)) {
//           setRetryCount(prev => prev + 1);
//         }

//         // Get user-friendly error message
//         const errorMessage = getClerkErrorMessage(error);
        
//         setResult({ 
//           success: false, 
//           error: errorMessage,
//           timestamp: new Date()
//         });

//         // Auto-retry for transient errors with exponential backoff
//         if (shouldRetry(error)) {
//           const retryDelay = Math.pow(2, retryCount) * 1000; // 1s, 2s, 4s
//           setTimeout(() => {
//             onSubmit(data); // Retry the same data
//           }, retryDelay);
//         }
//       }
//     });
//   };

//   // If user signed up with SSO, show different UI
//   if (ssoProvider) {
//     return (
//       <Card>
//         <CardHeader>
//           <CardTitle className="flex items-center gap-2">
//             <Lock className="h-5 w-5" />
//             Schimbă Parola
//           </CardTitle>
//           <CardDescription>
//             Parola este gestionată de furnizorul SSO
//           </CardDescription>
//         </CardHeader>
//         <CardContent>
//           <Alert>
//             <Shield className="h-4 w-4" />
//             <AlertDescription>
//               <div className="space-y-2">
//                 <p>
//                   <strong>Parola este gestionată de {ssoProvider === 'microsoft' ? 'Microsoft' : ssoProvider === 'google' ? 'Google' : ssoProvider}.</strong>
//                 </p>
//                 <p className="text-sm text-muted-foreground">
//                   Pentru a schimba parola, accesați setările contului dvs. {ssoProvider === 'microsoft' ? 'Microsoft' : ssoProvider === 'google' ? 'Google' : ssoProvider}.
//                 </p>
//                 <div className="mt-4">
//                   <Button
//                     variant="outline"
//                     size="sm"
//                     onClick={() => {
//                       const urls = {
//                         google: 'https://myaccount.google.com/security',
//                         microsoft: 'https://account.microsoft.com/security',
//                         facebook: 'https://www.facebook.com/settings?tab=security',
//                         github: 'https://github.com/settings/security',
//                         apple: 'https://appleid.apple.com/account/manage'
//                       };
//                       window.open(urls[ssoProvider as keyof typeof urls] || '#', '_blank');
//                     }}
//                   >
//                     Accesează setările {ssoProvider === 'microsoft' ? 'Microsoft' : ssoProvider === 'google' ? 'Google' : ssoProvider}
//                   </Button>
//                 </div>
//               </div>
//             </AlertDescription>
//           </Alert>
//         </CardContent>
//       </Card>
//     );
//   }

//   return (
//     <Card>
//       <CardHeader>
//         <CardTitle className="flex items-center gap-2">
//           <Lock className="h-5 w-5" />
//           Schimbă Parola
//         </CardTitle>
//         <CardDescription>
//           Actualizați parola pentru a vă menține contul în siguranță
//           {lastPasswordChange && (
//             <span className="block mt-1 text-xs text-muted-foreground">
//               Ultima schimbare: {new Date(lastPasswordChange).toLocaleDateString('ro-RO', {
//                 year: 'numeric',
//                 month: 'long',
//                 day: 'numeric',
//                 hour: '2-digit',
//                 minute: '2-digit'
//               })}
//             </span>
//           )}
//         </CardDescription>
//       </CardHeader>
//       <CardContent>
//         <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
//           {/* Success/Error Messages */}
//           {result && (
//             <Alert variant={result.success ? "default" : "destructive"}>
//               {result.success ? (
//                 <CheckCircle className="h-4 w-4" />
//               ) : (
//                 <AlertCircle className="h-4 w-4" />
//               )}
//               <AlertDescription>
//                 {result.success 
//                   ? "Parola a fost schimbată cu succes!" 
//                   : result.error
//                 }
//               </AlertDescription>
//             </Alert>
//           )}

//           {/* Loading state for user data */}
//           {!isLoaded && (
//             <Alert>
//               <Info className="h-4 w-4" />
//               <AlertDescription>
//                 Se încarcă datele utilizatorului...
//               </AlertDescription>
//             </Alert>
//           )}

//           {/* Current Password */}
//           <div className="space-y-2">
//             <Label htmlFor="currentPassword">Parola curentă *</Label>
//             <div className="relative">
//               <Input
//                 id="currentPassword"
//                 type={showCurrentPassword ? "text" : "password"}
//                 {...register("currentPassword")}
//                 className={errors.currentPassword ? "border-red-500 pr-10" : "pr-10"}
//                 placeholder="Introduceți parola curentă"
//                 disabled={isPending || !isLoaded}
//                 autoComplete="current-password"
//               />
//               <Button
//                 type="button"
//                 variant="ghost"
//                 size="sm"
//                 className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
//                 onClick={() => setShowCurrentPassword(!showCurrentPassword)}
//                 disabled={isPending}
//                 tabIndex={-1}
//               >
//                 {showCurrentPassword ? (
//                   <EyeOff className="h-4 w-4" />
//                 ) : (
//                   <Eye className="h-4 w-4" />
//                 )}
//               </Button>
//             </div>
//             {errors.currentPassword && (
//               <p className="text-sm text-red-600">{errors.currentPassword.message}</p>
//             )}
//           </div>

//           {/* New Password */}
//           <div className="space-y-2">
//             <Label htmlFor="newPassword">Parola nouă *</Label>
//             <div className="relative">
//               <Input
//                 id="newPassword"
//                 type={showNewPassword ? "text" : "password"}
//                 {...register("newPassword")}
//                 className={errors.newPassword ? "border-red-500 pr-10" : "pr-10"}
//                 placeholder="Introduceți parola nouă"
//                 disabled={isPending || !isLoaded}
//                 autoComplete="new-password"
//               />
//               <Button
//                 type="button"
//                 variant="ghost"
//                 size="sm"
//                 className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
//                 onClick={() => setShowNewPassword(!showNewPassword)}
//                 disabled={isPending}
//                 tabIndex={-1}
//               >
//                 {showNewPassword ? (
//                   <EyeOff className="h-4 w-4" />
//                 ) : (
//                   <Eye className="h-4 w-4" />
//                 )}
//               </Button>
//             </div>
//             {errors.newPassword && (
//               <p className="text-sm text-red-600">{errors.newPassword.message}</p>
//             )}

//             {/* Password Strength Indicator */}
//             {newPassword && (
//               <div className="space-y-2">
//                 <div className="flex items-center justify-between text-sm">
//                   <span>Puterea parolei:</span>
//                   <span className={`font-medium ${
//                     passwordStrength.score < 40 ? 'text-red-600' :
//                     passwordStrength.score < 60 ? 'text-orange-600' :
//                     passwordStrength.score < 80 ? 'text-yellow-600' :
//                     'text-green-600'
//                   }`}>
//                     {  getStrengthText(passwordStrength.score)}
//                   </span>
//                 </div>
//                 <Progress 
//                   value={passwordStrength.score} 
//                   className="h-2"
//                 />
//                 {passwordStrength.feedback.length > 0 && (
//                   <div className="text-xs text-muted-foreground">
//                     <p>Parola trebuie să conțină:</p>
//                     <ul className="list-disc list-inside ml-2">
//                       {passwordStrength.feedback.map((item, index) => (
//                         <li key={index}>{item}</li>
//                       ))}
//                     </ul>
//                   </div>
//                 )}
//               </div>
//             )}
//           </div>

//           {/* Confirm Password */}
//           <div className="space-y-2">
//             <Label htmlFor="confirmPassword">Confirmă parola nouă *</Label>
//             <div className="relative">
//               <Input
//                 id="confirmPassword"
//                 type={showConfirmPassword ? "text" : "password"}
//                 {...register("confirmPassword")}
//                 className={errors.confirmPassword ? "border-red-500 pr-10" : "pr-10"}
//                 placeholder="Confirmați parola nouă"
//                 disabled={isPending || !isLoaded}
//                 autoComplete="new-password"
//               />
//               <Button
//                 type="button"
//                 variant="ghost"
//                 size="sm"
//                 className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
//                 onClick={() => setShowConfirmPassword(!showConfirmPassword)}
//                 disabled={isPending}
//                 tabIndex={-1}
//               >
//                 {showConfirmPassword ? (
//                   <EyeOff className="h-4 w-4" />
//                 ) : (
//                   <Eye className="h-4 w-4" />
//                 )}
//               </Button>
//             </div>
//             {errors.confirmPassword && (
//               <p className="text-sm text-red-600">{errors.confirmPassword.message}</p>
//             )}
//           </div>

//           {/* Security Tips */}
//           <Alert>
//             <Shield className="h-4 w-4" />
//             <AlertDescription>
//               <strong>Sfaturi pentru securitate:</strong>
//               <ul className="list-disc list-inside mt-1 text-sm space-y-1">
//                 <li>Folosiți o parolă unică pentru acest cont</li>
//                 <li>Nu împărtășiți parola cu nimeni</li>
//                 <li>Schimbați parola regulat</li>
//                 <li>Activați autentificarea cu doi factori pentru securitate suplimentară</li>
//               </ul>
//             </AlertDescription>
//           </Alert>

//           {/* Submit Button */}
//           <div className="flex justify-end">
//             <Button 
//               type="submit" 
//               disabled={isPending || !isDirty || passwordStrength.score < 80}
//               className="bg-[#0066B1] hover:bg-[#004d85]"
//             >
//               {isPending ? (
//                 <>
//                   <Loader2 className="mr-2 h-4 w-4 animate-spin" />
//                   Se schimbă...
//                 </>
//               ) : (
//                 "Schimbă parola"
//               )}
//             </Button>
//           </div>
//         </form>
//       </CardContent>
//     </Card>
//   );
// }

















// "use client";

// import { useState, useTransition } from "react";
// import { useForm } from "react-hook-form";
// import { zodResolver } from "@hookform/resolvers/zod";
// import { Button } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
// import { Label } from "@/components/ui/label";
// import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
// import { Alert, AlertDescription } from "@/components/ui/alert";
// import { Progress } from "@/components/ui/progress";
// import { Loader2, CheckCircle, AlertCircle, Eye, EyeOff, Shield, Lock } from "lucide-react";
// import { passwordChangeSchema, type PasswordChangeInput } from "@/lib/zod";
// import { changePassword, type ActionResult } from "@/app/actions/account";

// interface PasswordFormProps {
//   lastPasswordChange?: Date | null;
//   ssoProvider?: string | null;
// }

// export default function PasswordForm({ lastPasswordChange, ssoProvider }: PasswordFormProps) {
//   const [isPending, startTransition] = useTransition();
//   const [result, setResult] = useState<ActionResult | null>(null);
//   const [showCurrentPassword, setShowCurrentPassword] = useState(false);
//   const [showNewPassword, setShowNewPassword] = useState(false);
//   const [showConfirmPassword, setShowConfirmPassword] = useState(false);

//   const {
//     register,
//     handleSubmit,
//     watch,
//     reset,
//     formState: { errors, isDirty }
//   } = useForm<PasswordChangeInput>({
//     resolver: zodResolver(passwordChangeSchema),
//     defaultValues: {
//       currentPassword: "",
//       newPassword: "",
//       confirmPassword: "",
//     }
//   });

//   const newPassword = watch("newPassword");

//   // Password strength calculation
//   const calculatePasswordStrength = (password: string): { score: number; feedback: string[] } => {
//     const feedback: string[] = [];
//     let score = 0;

//     if (password.length >= 8) {
//       score += 20;
//     } else {
//       feedback.push("Cel puțin 8 caractere");
//     }

//     if (/[a-z]/.test(password)) {
//       score += 20;
//     } else {
//       feedback.push("O literă mică");
//     }

//     if (/[A-Z]/.test(password)) {
//       score += 20;
//     } else {
//       feedback.push("O literă mare");
//     }

//     if (/\d/.test(password)) {
//       score += 20;
//     } else {
//       feedback.push("O cifră");
//     }

//     if (/[@$!%*?&]/.test(password)) {
//       score += 20;
//     } else {
//       feedback.push("Un caracter special (@$!%*?&)");
//     }

//     return { score, feedback };
//   };

//   const passwordStrength = calculatePasswordStrength(newPassword || "");

//   const getStrengthColor = (score: number) => {
//     if (score < 40) return "bg-red-500";
//     if (score < 60) return "bg-orange-500";
//     if (score < 80) return "bg-yellow-500";
//     return "bg-green-500";
//   };

//   const getStrengthText = (score: number) => {
//     if (score < 40) return "Slabă";
//     if (score < 60) return "Medie";
//     if (score < 80) return "Bună";
//     return "Foarte bună";
//   };

//   const onSubmit = (data: PasswordChangeInput) => {
//     startTransition(async () => {
//       try {
//         const result = await changePassword(data);
//         setResult(result);
        
//         if (result.success) {
//           reset(); // Clear form on success
//           setTimeout(() => setResult(null), 5000);
//         }
//       } catch (error) {
//         setResult({
//           success: false,
//           error: "A apărut o eroare neașteptată. Vă rugăm să încercați din nou."
//         });
//       }
//     });
//   };

//   // If user signed up with SSO, show different UI
//   if (ssoProvider) {
//     return (
//       <Card>
//         <CardHeader>
//           <CardTitle className="flex items-center gap-2">
//             <Lock className="h-5 w-5" />
//             Schimbă Parola
//           </CardTitle>
//           <CardDescription>
//             Parola este gestionată de furnizorul SSO
//           </CardDescription>
//         </CardHeader>
//         <CardContent>
//           <Alert>
//             <Shield className="h-4 w-4" />
//             <AlertDescription>
//               <div className="space-y-2">
//                 <p>
//                   <strong>Parola este gestionată de {ssoProvider === 'microsoft' ? 'Microsoft' : ssoProvider === 'google' ? 'Google' : ssoProvider}.</strong>
//                 </p>
//                 <p className="text-sm text-muted-foreground">
//                   Pentru a schimba parola, accesați setările contului dvs. {ssoProvider === 'microsoft' ? 'Microsoft' : ssoProvider === 'google' ? 'Google' : ssoProvider}.
//                 </p>
//                 <div className="mt-4">
//                   <Button
//                     variant="outline"
//                     size="sm"
//                     onClick={() => {
//                       const urls = {
//                         google: 'https://myaccount.google.com/security',
//                         microsoft: 'https://account.microsoft.com/security',
//                         facebook: 'https://www.facebook.com/settings?tab=security',
//                         github: 'https://github.com/settings/security',
//                         apple: 'https://appleid.apple.com/account/manage'
//                       };
//                       window.open(urls[ssoProvider as keyof typeof urls] || '#', '_blank');
//                     }}
//                   >
//                     Accesează setările {ssoProvider === 'microsoft' ? 'Microsoft' : ssoProvider === 'google' ? 'Google' : ssoProvider}
//                   </Button>
//                 </div>
//               </div>
//             </AlertDescription>
//           </Alert>
//         </CardContent>
//       </Card>
//     );
//   }

//   return (
//     <Card>
//       <CardHeader>
//         <CardTitle className="flex items-center gap-2">
//           <Lock className="h-5 w-5" />
//           Schimbă Parola
//         </CardTitle>
//         <CardDescription>
//           Actualizați parola pentru a vă menține contul în siguranță
//           {lastPasswordChange && (
//             <span className="block mt-1 text-xs text-muted-foreground">
//               Ultima schimbare: {new Date(lastPasswordChange).toLocaleDateString('ro-RO')}
//             </span>
//           )}
//         </CardDescription>
//       </CardHeader>
//       <CardContent>
//         <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
//           {/* Success/Error Messages */}
//           {result && (
//             <Alert variant={result.success ? "default" : "destructive"}>
//               {result.success ? (
//                 <CheckCircle className="h-4 w-4" />
//               ) : (
//                 <AlertCircle className="h-4 w-4" />
//               )}
//               <AlertDescription>
//                 {result.success 
//                   ? "Parola a fost schimbată cu succes!" 
//                   : result.error
//                 }
//               </AlertDescription>
//             </Alert>
//           )}

//           {/* Current Password */}
//           <div className="space-y-2">
//             <Label htmlFor="currentPassword">Parola curentă *</Label>
//             <div className="relative">
//               <Input
//                 id="currentPassword"
//                 type={showCurrentPassword ? "text" : "password"}
//                 {...register("currentPassword")}
//                 className={errors.currentPassword ? "border-red-500 pr-10" : "pr-10"}
//                 placeholder="Introduceți parola curentă"
//               />
//               <Button
//                 type="button"
//                 variant="ghost"
//                 size="sm"
//                 className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
//                 onClick={() => setShowCurrentPassword(!showCurrentPassword)}
//               >
//                 {showCurrentPassword ? (
//                   <EyeOff className="h-4 w-4" />
//                 ) : (
//                   <Eye className="h-4 w-4" />
//                 )}
//               </Button>
//             </div>
//             {errors.currentPassword && (
//               <p className="text-sm text-red-600">{errors.currentPassword.message}</p>
//             )}
//           </div>

//           {/* New Password */}
//           <div className="space-y-2">
//             <Label htmlFor="newPassword">Parola nouă *</Label>
//             <div className="relative">
//               <Input
//                 id="newPassword"
//                 type={showNewPassword ? "text" : "password"}
//                 {...register("newPassword")}
//                 className={errors.newPassword ? "border-red-500 pr-10" : "pr-10"}
//                 placeholder="Introduceți parola nouă"
//               />
//               <Button
//                 type="button"
//                 variant="ghost"
//                 size="sm"
//                 className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
//                 onClick={() => setShowNewPassword(!showNewPassword)}
//               >
//                 {showNewPassword ? (
//                   <EyeOff className="h-4 w-4" />
//                 ) : (
//                   <Eye className="h-4 w-4" />
//                 )}
//               </Button>
//             </div>
//             {errors.newPassword && (
//               <p className="text-sm text-red-600">{errors.newPassword.message}</p>
//             )}

//             {/* Password Strength Indicator */}
//             {newPassword && (
//               <div className="space-y-2">
//                 <div className="flex items-center justify-between text-sm">
//                   <span>Puterea parolei:</span>
//                   <span className={`font-medium ${
//                     passwordStrength.score < 40 ? 'text-red-600' :
//                     passwordStrength.score < 60 ? 'text-orange-600' :
//                     passwordStrength.score < 80 ? 'text-yellow-600' :
//                     'text-green-600'
//                   }`}>
//                     {getStrengthText(passwordStrength.score)}
//                   </span>
//                 </div>
//                 <Progress 
//                   value={passwordStrength.score} 
//                   className="h-2"
//                 />
//                 {passwordStrength.feedback.length > 0 && (
//                   <div className="text-xs text-muted-foreground">
//                     <p>Parola trebuie să conțină:</p>
//                     <ul className="list-disc list-inside ml-2">
//                       {passwordStrength.feedback.map((item, index) => (
//                         <li key={index}>{item}</li>
//                       ))}
//                     </ul>
//                   </div>
//                 )}
//               </div>
//             )}
//           </div>

//           {/* Confirm Password */}
//           <div className="space-y-2">
//             <Label htmlFor="confirmPassword">Confirmă parola nouă *</Label>
//             <div className="relative">
//               <Input
//                 id="confirmPassword"
//                 type={showConfirmPassword ? "text" : "password"}
//                 {...register("confirmPassword")}
//                 className={errors.confirmPassword ? "border-red-500 pr-10" : "pr-10"}
//                 placeholder="Confirmați parola nouă"
//               />
//               <Button
//                 type="button"
//                 variant="ghost"
//                 size="sm"
//                 className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
//                 onClick={() => setShowConfirmPassword(!showConfirmPassword)}
//               >
//                 {showConfirmPassword ? (
//                   <EyeOff className="h-4 w-4" />
//                 ) : (
//                   <Eye className="h-4 w-4" />
//                 )}
//               </Button>
//             </div>
//             {errors.confirmPassword && (
//               <p className="text-sm text-red-600">{errors.confirmPassword.message}</p>
//             )}
//           </div>

//           {/* Security Tips */}
//           <Alert>
//             <Shield className="h-4 w-4" />
//             <AlertDescription>
//               <strong>Sfaturi pentru securitate:</strong>
//               <ul className="list-disc list-inside mt-1 text-sm space-y-1">
//                 <li>Folosiți o parolă unică pentru acest cont</li>
//                 <li>Nu împărtășiți parola cu nimeni</li>
//                 <li>Schimbați parola regulat</li>
//                 <li>Activați autentificarea cu doi factori pentru securitate suplimentară</li>
//               </ul>
//             </AlertDescription>
//           </Alert>

//           {/* Submit Button */}
//           <div className="flex justify-end">
//             <Button 
//               type="submit" 
//               disabled={isPending || !isDirty || passwordStrength.score < 80}
//               className="bg-[#0066B1] hover:bg-[#004d85]"
//             >
//               {isPending ? (
//                 <>
//                   <Loader2 className="mr-2 h-4 w-4 animate-spin" />
//                   Se schimbă...
//                 </>
//               ) : (
//                 "Schimbă parola"
//               )}
//             </Button>
//           </div>
//         </form>
//       </CardContent>
//     </Card>
//   );
// }
