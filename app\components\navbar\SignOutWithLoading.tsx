'use client';

import { useClerk } from "@clerk/nextjs";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { LogOut, Loader2 } from "lucide-react";

export function SignOutWithLoading() {
  const { signOut } = useClerk();
  const [loading, setLoading] = useState(false);

  const handleSignOut = async () => {
    setLoading(true);
    try {
      await signOut(() => {
        window.location.href = "/"; // optional redirect
      });
    } catch (err) {
      console.error("Sign out failed", err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      onClick={handleSignOut}
      variant="outline"
      className="w-full px-4 py-2 text-left text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 flex items-center gap-2"
      disabled={loading}
    >
      {loading ? (
        <Loader2 className="w-4 h-4 animate-spin text-red-400" />
      ) : (
        <LogOut className="w-4 h-4 text-red-400" />
      )}
      {loading ? "Deconectare..." : "Deconectare"}
    </Button>
  );
}
