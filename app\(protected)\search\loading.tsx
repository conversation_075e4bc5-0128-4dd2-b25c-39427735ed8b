import { Skeleton } from "@/components/ui/skeleton";

export default function SearchRoutePageSkeleton() {
  return (
    <div className="min-h-screen">
      <div className="max-w-[1640px] mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 animate-pulse">
      {/* Left Sidebar - Filters */}
      <div className="lg:col-span-1">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 space-y-6 border border-gray-100 dark:border-gray-700">
          {/* Filter Header */}
          <div className="flex items-center gap-2">
            <Skeleton className="h-5 w-5 rounded" />
            <Skeleton className="h-6 w-32" />
          </div>

          {/* Categories Filter */}
          <div>
            <Skeleton className="h-5 w-20 mb-3" />
            <div className="border rounded-md p-3">
              <Skeleton className="h-8 w-full mb-2" />
              <div className="space-y-2">
                {Array.from({ length: 5 }).map((_, i) => (
                  <div key={i} className="flex items-center space-x-2 p-1">
                    <Skeleton className="h-4 w-4 rounded" />
                    <Skeleton className="h-4 flex-1" />
                    <Skeleton className="h-4 w-8 rounded-full" />
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Brands Filter */}
          <div>
            <Skeleton className="h-5 w-16 mb-3" />
            <div className="border rounded-md p-3">
              <Skeleton className="h-8 w-full mb-2" />
              <div className="space-y-2">
                {Array.from({ length: 4 }).map((_, i) => (
                  <div key={i} className="flex items-center space-x-2 p-1">
                    <Skeleton className="h-4 w-4 rounded" />
                    <Skeleton className="h-4 flex-1" />
                    <Skeleton className="h-4 w-6 rounded-full" />
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Classes Filter */}
          <div>
            <Skeleton className="h-5 w-20 mb-3" />
            <div className="border rounded-md p-3">
              <Skeleton className="h-8 w-full mb-2" />
              <div className="space-y-2">
                {Array.from({ length: 6 }).map((_, i) => (
                  <div key={i} className="flex items-center space-x-2 p-1">
                    <Skeleton className="h-4 w-4 rounded" />
                    <Skeleton className="h-4 flex-1" />
                    <Skeleton className="h-4 w-8 rounded-full" />
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Attributes Filter */}
          <div>
            <Skeleton className="h-5 w-24 mb-3" />
            <div className="space-y-4">
              {Array.from({ length: 2 }).map((_, i) => (
                <div key={i} className="border rounded-md p-3">
                  <Skeleton className="h-4 w-20 mb-2" />
                  <div className="space-y-2">
                    {Array.from({ length: 3 }).map((_, j) => (
                      <div key={j} className="flex items-center space-x-2 p-1">
                        <Skeleton className="h-4 w-4 rounded" />
                        <Skeleton className="h-4 flex-1" />
                        <Skeleton className="h-4 w-6 rounded-full" />
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Price Range Filter */}
          <div>
            <Skeleton className="h-5 w-24 mb-3" />
            <div className="space-y-3">
              <Skeleton className="h-2 w-full rounded-full" />
              <div className="flex justify-between">
                <Skeleton className="h-4 w-12" />
                <Skeleton className="h-4 w-12" />
              </div>
            </div>
          </div>

          {/* Discount Filter */}
          <div>
            <div className="flex items-center space-x-2">
              <Skeleton className="h-4 w-4 rounded" />
              <Skeleton className="h-4 w-32" />
            </div>
          </div>
        </div>
      </div>

      {/* Right Content - Results */}
      <div className="lg:col-span-3">
        {/* Header with active filters and sort */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex-1">
              <Skeleton className="h-7 w-48 mb-2" />
              <Skeleton className="h-4 w-32 mb-3" />
              {/* Active filters skeleton */}
              <div className="flex flex-wrap gap-2">
                {Array.from({ length: 3 }).map((_, i) => (
                  <Skeleton key={i} className="h-6 w-20 rounded-full" />
                ))}
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-10 w-40 rounded-md" />
            </div>
          </div>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden border border-gray-100 dark:border-gray-700 hover:shadow-md transition-shadow">
              <div className="relative">
                <Skeleton className="h-48 w-full" />
                {/* Discount badge skeleton */}
                <div className="absolute top-2 right-2">
                  <Skeleton className="h-6 w-12 rounded-full" />
                </div>
              </div>
              <div className="p-4 space-y-3">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
                <div className="flex justify-between items-center pt-2">
                  <div className="space-y-1">
                    <Skeleton className="h-3 w-16" />
                    <Skeleton className="h-6 w-20" />
                  </div>
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-8 w-8 rounded-full" />
                    <Skeleton className="h-9 w-9 rounded-full" />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Pagination */}
        <div className="flex justify-center mt-8">
          <div className="flex items-center space-x-2">
            <Skeleton className="h-10 w-20 rounded" />
            {Array.from({ length: 5 }).map((_, i) => (
              <Skeleton key={i} className="h-10 w-10 rounded" />
            ))}
            <Skeleton className="h-10 w-20 rounded" />
          </div>
        </div>
      </div>
        </div>
      </div>
    </div>
  )
}