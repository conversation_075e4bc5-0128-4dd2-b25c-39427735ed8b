import { Skeleton } from "@/components/ui/skeleton";
// …other imports

export default function CartRoute() {
    return (
      <div className="min-h-screen py-12">
        <div className="max-w-[1640px] mx-auto px-4 sm:px-6 lg:px-8">
          {/* Page title skeleton */}
          <Skeleton className="h-8 w-1/3 mb-8" />

          <div className="flex flex-col lg:flex-row items-start justify-between gap-8">
            {/* Cart Items Skeleton */}
            <div className="w-full lg:w-2/3 border rounded-lg shadow p-6 space-y-6">
              {Array.from({ length: 3 }).map((_, i) => (
                <div
                  key={i}
                  className="flex items-center gap-6 p-6 border-b last:border-0"
                >
                  {/* Thumbnail */}
                  <Skeleton className="w-24 h-24 rounded-md" />

                  {/* Details */}
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-5 w-1/2" />
                    <Skeleton className="h-4 w-1/3" />
                    <div className="flex justify-between">
                      <Skeleton className="h-6 w-24" />
                      <Skeleton className="h-4 w-16" />
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Order Summary Skeleton */}
            <div className="w-full lg:w-1/3 border rounded-lg shadow p-6 h-fit space-y-4">
              <Skeleton className="h-6 w-1/2 mb-4" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-full" />
              <div className="border-t border-gray-200 pt-4">
                <Skeleton className="h-6 w-1/2" />
              </div>
              <Skeleton className="h-10 w-full mt-4" />
            </div>
          </div>
        </div>
      </div>
    );
}
