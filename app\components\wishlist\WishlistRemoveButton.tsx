"use client"

import { Loader2, Trash } from "lucide-react"
import { useTransition } from 'react';
import { toast } from "sonner"
import { useWishlist } from '@/app/context/WishlistContext';
import { Button } from '@/components/ui/button';
import { removeFromWishlist } from "@/app/actions/wishlist";

export default function RemoveProductWishlist({product}: {product: string}){
  const [isPending, startTransition] = useTransition();
  const { updateActualWishlist } = useWishlist();

  const handleWishlistToggle = () => {
    startTransition(async () => {

      try {
        const response = await removeFromWishlist(product);
        if (response.success) {
          toast.success('Șters cu succes din lista de favorite.');
          updateActualWishlist(product, false);
        } else {
          toast.error('Failed to remove from wishlist');
        }
      } catch {
        toast.error('Nu s-a putut actualiza lista de favorite.');
      }
    });
  };

  return(
    <>
      {isPending ? (
        <Button 
          disabled 
          variant={"outline"}
          size="sm" 
          className="gap-2"
        >
          <Loader2 className="h-5 w-5 animate-spin" /> Se sterge
        </Button>
      ) : (
        <Button 
          onClick={handleWishlistToggle}
          variant="outline"
          size="sm" 
          className="gap-2 text-red-600 hover:text-red-700 disabled:opacity-50"
        >
          <Trash className="w-4 h-4" /> Sterge
        </Button>
      )}
    </>
  )
}
// "use client"

// import { Trash } from "lucide-react"
// import { useRouter } from 'next/navigation';
// import { useTransition } from 'react';
// import { toast } from "sonner"
// import { useWishlist } from '@/app/context/WishlistContext';
// import { Button } from '@/components/ui/button';
// import { removeFromWishlist } from "@/app/actions/wishlist";

// export default function RemoveProductWishlist({product}: {product: string}){
//   const [isPending, startTransition] = useTransition();
//   const router = useRouter();
//   const {
//     isInWishlist,
//     removeFromWishlistOptimistic,
//     revertOptimisticUpdate,
//     updateActualWishlist
//   } = useWishlist();

//   const handleWishlistToggle = () => {
//     startTransition(async () => {
//       const wasInWishlist = isInWishlist(product);

//       // Optimistically remove from wishlist
//       removeFromWishlistOptimistic(product);

//       try {
//         const response = await removeFromWishlist(product);
//         if (response.success) {
//           // Update the actual wishlist
//           updateActualWishlist(product, false);
//           toast.success('Șters cu succes din lista de favorite.');
//           router.refresh();
//         } else {
//           throw new Error('Failed to remove from wishlist');
//         }
//       } catch (error) {
//         // Revert optimistic update on error
//         revertOptimisticUpdate(product, wasInWishlist);
//         toast.error('Nu s-a putut actualiza lista de favorite.');
//         console.error('Remove from wishlist failed:', error);
//       }
//     });
//   };

//   // Don't render if item is not in wishlist (optimistically removed)
//   if (!isInWishlist(product)) {
//     return null;
//   }

//   return(
//     <>
//     <Button
//         onClick={handleWishlistToggle}
//         disabled={isPending}
//         aria-label={"Sterge din favorite"}
//         variant="outline"
//         size="sm"
//         className="gap-2 text-red-600 hover:text-red-700 disabled:opacity-50"
//         >
//         <Trash className="w-4 h-4" />
//         {isPending ? 'Se șterge...' : 'Sterge'}
//     </Button>
//     </>
//   )
// }