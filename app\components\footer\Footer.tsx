//import Image from 'next/image';
import { FaFacebook, FaInstagram ,FaTwitter } from 'react-icons/fa'; 

const Footer = () => {
  return (
    <footer className="dark:bg-gradient-to-b from-gray-900 to-black dark:text-gray-300 border py-16">
      <div className="max-w-[1640px] mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-10">
          <div>
            <h3 className="text-white font-semibold mb-4 text-lg border-b border-[#0066B1] pb-2 inline-block">
              About Us
            </h3>
            <p className="text-sm leading-relaxed">
              Your trusted source for genuine BMW parts and accessories since
              1990. We pride ourselves on quality, authenticity, and exceptional
              customer service.
            </p>
            <div className="mt-6">
              {/* <Image
                src="https://images.unsplash.com/photo-1617886903355-9354bb00d5e5?w=200&h=50&fit=crop&crop=edges"
                alt="BMW Parts Store"
                className="h-10"
                width={200}
                height={50}
              /> */}
            </div>
          </div>
          <div>
            <h3 className="text-white font-semibold mb-4 text-lg border-b border-[#0066B1] pb-2 inline-block">
              Quick Links
            </h3>
            <ul className="space-y-3 text-sm">
              <li>
                <a
                  href="#"
                  className="hover:text-white hover:translate-x-1 transition-transform duration-200 flex items-center"
                >
                  <span className="w-1 h-1 bg-[#0066B1] rounded-full mr-2"></span>
                  Shop All Parts
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-white hover:translate-x-1 transition-transform duration-200 flex items-center"
                >
                  <span className="w-1 h-1 bg-[#0066B1] rounded-full mr-2"></span>
                  Track Order
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-white hover:translate-x-1 transition-transform duration-200 flex items-center"
                >
                  <span className="w-1 h-1 bg-[#0066B1] rounded-full mr-2"></span>
                  Returns
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-white hover:translate-x-1 transition-transform duration-200 flex items-center"
                >
                  <span className="w-1 h-1 bg-[#0066B1] rounded-full mr-2"></span>
                  Shipping Policy
                </a>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="text-white font-semibold mb-4 text-lg border-b border-[#0066B1] pb-2 inline-block">
              Customer Service
            </h3>
            <ul className="space-y-3 text-sm">
              <li>
                <a
                  href="#"
                  className="hover:text-white hover:translate-x-1 transition-transform duration-200 flex items-center"
                >
                  <span className="w-1 h-1 bg-[#0066B1] rounded-full mr-2"></span>
                  Contact Us
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-white hover:translate-x-1 transition-transform duration-200 flex items-center"
                >
                  <span className="w-1 h-1 bg-[#0066B1] rounded-full mr-2"></span>
                  FAQ
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-white hover:translate-x-1 transition-transform duration-200 flex items-center"
                >
                  <span className="w-1 h-1 bg-[#0066B1] rounded-full mr-2"></span>
                  Warranty
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="hover:text-white hover:translate-x-1 transition-transform duration-200 flex items-center"
                >
                  <span className="w-1 h-1 bg-[#0066B1] rounded-full mr-2"></span>
                  Support
                </a>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="text-white font-semibold mb-4 text-lg border-b border-[#0066B1] pb-2 inline-block">
              Connect With Us
            </h3>
            <div className="flex space-x-4 mb-6">
              <a
                href="#"
                className="bg-gray-800 p-2 rounded-full hover:bg-[#0066B1] transition-colors duration-300"
              >
                <FaFacebook size={20} />
              </a>
              <a
                href="#"
                className="bg-gray-800 p-2 rounded-full hover:bg-[#0066B1] transition-colors duration-300"
              >
                <FaInstagram size={20} />
              </a>
              <a
                href="#"
                className="bg-gray-800 p-2 rounded-full hover:bg-[#0066B1] transition-colors duration-300"
              >
                <FaTwitter size={20} />
              </a>
              <a
                href="#"
                className="bg-gray-800 p-2 rounded-full hover:bg-[#0066B1] transition-colors duration-300"
              >
                <FaTwitter size={20} />
              </a>
            </div>
            <div className="text-sm">
              <p className="mb-2">
                <strong>Email:</strong> <EMAIL>
              </p>
              <p>
                <strong>Phone:</strong> +****************
              </p>
            </div>
          </div>
        </div>
        <div className="border-t border-gray-800 mt-10 pt-8 text-sm text-center">
          <p>
            © 2024 BMW Parts Store. All rights reserved. |{" "}
            <a href="#" className="text-[#0066B1] hover:underline">
              Privacy Policy
            </a>{" "}
            |{" "}
            <a href="#" className="text-[#0066B1] hover:underline">
              Terms of Service
            </a>
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
