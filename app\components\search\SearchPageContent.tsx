import { SearchPageData } from "@/types/search"
import SearchFilters from "./SearchFilters"
import SearchHeader from "./SearchHeader"
import SearchResults from "./SearchResults"
import SearchPagination from "./SearchPagination"

interface SearchPageContentProps {
  searchData: SearchPageData
}

export default function SearchPageContent({ searchData }: SearchPageContentProps) {
  const { products, categories, brands, classes, attributes, priceRange, pagination, appliedFilters, has4th } = searchData

  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
      {/* Left Sidebar - Filters */}
      <div className="lg:col-span-1">
        <SearchFilters
          has4th={has4th}
          categories={categories}
          brands={brands}
          classes={classes}
          attributes={attributes}
          priceRange={priceRange}
          appliedFilters={appliedFilters}
        />
      </div>

      {/* Right Content - Results */}
      <div className="lg:col-span-3">
        <SearchHeader
          query={appliedFilters.query}
          totalResults={pagination.total}
          appliedFilters={appliedFilters}
          searchData={searchData}
        />

        <SearchResults products={products} />

        {pagination.pages > 1 && (
          <SearchPagination pagination={pagination} />
        )}
      </div>
    </div>
  )
}
