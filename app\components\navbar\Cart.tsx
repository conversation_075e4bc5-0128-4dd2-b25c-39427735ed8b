"server-only"

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { ShoppingCart } from "lucide-react";
import CartPreview from "./CartPreview";
import Link from "next/link";
import { CartItem } from "@/types/cart";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";

export function CartComponent({ cartItems }: { cartItems: CartItem[] }) {

  const cartCount = cartItems.reduce((acc, item) => acc + item.quantity, 0) || 0;

  return (
    // Use the HoverCard component as the main wrapper
    <HoverCard openDelay={200} closeDelay={100}>
      <HoverCardTrigger asChild>
        <Link href="/cart">
        <Button
          variant="ghost"
          size="icon"
          className="relative hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <ShoppingCart className="h-6 w-6 text-[#4D4D4D] dark:text-gray-300" />
          <Badge className="absolute -top-2 -right-2 bg-[#0066B1]" variant="secondary">
            {cartCount}
          </Badge>
        </Button>
        </Link>
      </HoverCardTrigger>

      <HoverCardContent className="w-auto p-0 border-none" align="end" sideOffset={5}>
        <CartPreview cartCount={cartCount} items={cartItems} />

      </HoverCardContent>
    </HoverCard>
  );
}
