"server-only"

import { CartComponent } from "./Cart";
import { LogoComponent } from "./Logo";
import SearchSection from "./SearchSection";
import { UserDropdown } from "./UserDropdown";
import { DarkModeButton } from "./DarkModeButton";
import { WishlistComponent } from "../wishlist/WishlistCount";
import { CartItem } from "@/types/cart";
import { User } from "@/generated/prisma";

interface NavbarClientSideProps {
  cartItems: CartItem[];
  query?: string;
  user: User | null;
}

export function NavbarClientSide( { cartItems, query, user }: NavbarClientSideProps) {

  return (

    <div className="flex items-center justify-between px-6 py-4 sticky top-0 z-50">

          {/* Logo Section */}
          <LogoComponent />

          {/* Search Section */}
          <div className="flex-1 mx-8">
            <SearchSection query={query} />
          </div>

          {/* Wishlist, Cart, Account */}
          <div className="flex items-center space-x-4">

            {/* Dark Mode Button */}
            <DarkModeButton />
            
            {/* Wishlist */}
            <WishlistComponent />

            {/* Cart */}
            <CartComponent cartItems={cartItems} />

            {/* Account */} 
            <div className="flex justify-end items-center p-4 gap-4 h-16">
              <UserDropdown user={user} />
            </div>
            
          </div>
        </div>
  )
}