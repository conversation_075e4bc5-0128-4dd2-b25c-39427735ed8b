"use client"

import { useState, useEffect, use<PERSON>allback, useRef } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { ScrollArea } from "@/components/ui/scroll-area"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Filter } from "lucide-react"
import { SearchFilters as SearchFiltersType } from "@/types/search"

interface SearchFiltersProps {
  has4th: boolean
  categories: Array<{
    id: string
    name: string
    slug: string | null
    productCount: number
  }>
  brands: Array<{
    id: string
    name: string
    productCount: number
  }>
  classes: Array<{
    id: string
    name: string
    productCount: number
    brandId?: string
    originalClassId?: string
  }>
  attributes: Array<{
    key: string
    values: Array<{
      value: string
      productCount: number
    }>
  }>
  priceRange: {
    min: number
    max: number
  }
  appliedFilters: SearchFiltersType
}

export default function SearchFilters({
  has4th,
  categories,
  brands,
  classes,
  attributes,
  priceRange,
  appliedFilters
}: SearchFiltersProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const isUpdatingFilters = useRef(false)

  const [priceValues, setPriceValues] = useState([
    appliedFilters.minPrice || priceRange.min,
    appliedFilters.maxPrice || priceRange.max
  ])

  // Update price values when priceRange changes (due to filter changes)
  useEffect(() => {
    setPriceValues([
      appliedFilters.minPrice || priceRange.min,
      appliedFilters.maxPrice || priceRange.max
    ])
  }, [priceRange.min, priceRange.max, appliedFilters.minPrice, appliedFilters.maxPrice])

  // Track if this is the initial load to prevent unwanted debounce triggers
  const [isInitialLoad, setIsInitialLoad] = useState(true)

  // Reset initial load flag after component mounts and price values are set
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsInitialLoad(false)
    }, 100) // Small delay to ensure price values are properly set

    return () => clearTimeout(timer)
  }, [])

  const updateFilters = useCallback((updates: Record<string, string | string[] | boolean | undefined>) => {
    isUpdatingFilters.current = true

    const params = new URLSearchParams(searchParams.toString())

    Object.entries(updates).forEach(([key, value]) => {
      if (value === undefined || value === '' || (Array.isArray(value) && value.length === 0)) {
        params.delete(key)
      } else if (Array.isArray(value)) {
        params.set(key, value.join(','))
      } else {
        // For category3Id, use the ID directly (no encoding needed)
        if (key === 'category3Id') {
          params.set(key, value.toString())
        } else if (key === 'query') {
          params.set(key, encodeURIComponent(value.toString()))
        } else {
          params.set(key, value.toString())
        }
      }
    })

    params.delete('page') // Reset to first page when filtering
    // Always navigate to search route
    router.push(`/search?${params.toString()}`)

    setTimeout(() => {
      isUpdatingFilters.current = false
  }, 100)
  }, [router, searchParams])

  const handleCategoryChange = (categoryId: string) => {
    // Use the category ID directly instead of name to handle duplicates
    const currentCategoryId = appliedFilters.category3Id
    const newCategoryId = categoryId === currentCategoryId ? undefined : categoryId

    updateFilters({ category3Id: newCategoryId })
  }

  const handleBrandToggle = (brandId: string) => {
    // Find the brand to get its name
    const selectedBrand = brands.find(brand => brand.id === brandId)
    const brandName = selectedBrand?.name || brandId

    const currentBrands = appliedFilters.brands || []
    const newBrands = currentBrands.includes(brandName)
      ? currentBrands.filter(name => name !== brandName)
      : [...currentBrands, brandName]

    updateFilters({ brands: newBrands })
  }

  const handleClassToggle = (classId: string) => {
    // Find the class to get its name
    const selectedClass = classes.find(cls => cls.id === classId)
    const className = selectedClass?.name || classId

    const currentClasses = appliedFilters.classes || []
    const newClasses = currentClasses.includes(className)
      ? currentClasses.filter(name => name !== className)
      : [...currentClasses, className]

    updateFilters({ classes: newClasses })
  }

  // Debounced price update
  useEffect(() => {
    if (isInitialLoad || isUpdatingFilters.current) return

    const timer = setTimeout(() => {
      const [min, max] = priceValues

      if (min !== (appliedFilters.minPrice || priceRange.min) || 
              max !== (appliedFilters.maxPrice || priceRange.max)) {
            updateFilters({
              minPrice: min !== priceRange.min ? min.toString() : undefined,
              maxPrice: max !== priceRange.max ? max.toString() : undefined
            })
          }
    }, 500) // 500ms debounce

    return () => clearTimeout(timer)
  }, [priceValues, isInitialLoad, priceRange.min, priceRange.max, appliedFilters.minPrice, appliedFilters.maxPrice, updateFilters])

  const handlePriceChange = (values: number[]) => {
    setPriceValues(values)
  }

  const handleDiscountToggle = () => {
    updateFilters({ hasDiscount: !appliedFilters.hasDiscount })
  }

  const handleAttributeToggle = (key: string, value: string) => {
    const currentAttributes = appliedFilters.attributes || {}
    const currentValues = currentAttributes[key] || []

    let newValues: string[]
    if (currentValues.includes(value)) {
      // Remove value
      newValues = currentValues.filter(v => v !== value)
    } else {
      // Add value
      newValues = [...currentValues, value]
    }

    const newAttributes = { ...currentAttributes }
    if (newValues.length === 0) {
      delete newAttributes[key]
    } else {
      newAttributes[key] = newValues
    }

    // Convert to URL format: key1:value1,value2|key2:value3
    const attributesString = Object.entries(newAttributes)
      .map(([k, values]) => `${k}:${values.join(',')}`)
      .join('|')

    updateFilters({
      attributes: attributesString || undefined
    })
  }

  // Filter brands based on selected classes
  const filteredBrands = brands.filter(brand => {
    // If no classes are selected, show all brands
    if (!appliedFilters.classes?.length) {
      return true
    }
    // If classes are selected, only show brands that have products in those classes
    return classes.some(cls =>
      cls.brandId === brand.id &&
      appliedFilters.classes?.includes(cls.name)
    )
  })

  // Filter classes based on selected brands
  const filteredClasses = classes.filter(cls => {
    // If no brands are selected, show all classes
    if (!appliedFilters.brands?.length) {
      return true
    }
    // If brands are selected, only show classes that belong to selected brands
    // Find the brand name for this class
    const classBrand = brands.find(brand => brand.id === cls.brandId)
    return appliedFilters.brands.includes(classBrand?.name || '')
  })

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-2">
        <Filter className="h-5 w-5 text-gray-600 dark:text-gray-400" />
        <h3 className="font-semibold text-gray-900 dark:text-gray-100">Filtre Cautare</h3>
      </div>

      {/* Categories */}
      {categories.length > 0 && (
        <div>
          <Label className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3 block">
            Categorii
          </Label>
          <RadioGroup
            value={appliedFilters.category3Id || ''}
            onValueChange={handleCategoryChange}
          >
            <ScrollArea className="h-48">
              <div className="space-y-2">
                {categories.map((category) => (
                  <div
                    key={category.id}
                    className="flex items-center space-x-2"
                  >
                    <RadioGroupItem value={category.id} id={`cat-${category.id}`} />
                    <Label
                      htmlFor={`cat-${category.id}`}
                      className="flex-1 text-sm cursor-pointer flex justify-between items-center"
                    >
                      <span className="truncate w-0 flex-1">{category.name}</span>
                      <Badge variant="secondary" className="ml-2 text-xs">
                        {category.productCount}
                      </Badge>
                    </Label>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </RadioGroup>
        </div>
      )}

      {/* Brands */}
      {filteredBrands.length > 0 && (
        <div>
          <Label className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3 block">
            Branduri
          </Label>
          <div className="border rounded-md">
            <Command>
              <CommandInput placeholder="Cauta brand..." />
              <CommandEmpty>Nu s-au gasit branduri.</CommandEmpty>
              <CommandList>
                <CommandGroup>
                  <ScrollArea className="h-48">
                    {filteredBrands.map((brand) => (
                      <CommandItem
                        key={brand.id}
                        onSelect={() => handleBrandToggle(brand.id)}
                        className="flex items-center space-x-2"
                      >
                        <Checkbox
                          checked={appliedFilters.brands?.includes(brand.name) || false}
                          onChange={() => handleBrandToggle(brand.id)}
                        />
                        <span className="flex-1 text-sm">{brand.name}</span>
                        <Badge variant="secondary" className="text-xs">
                          {brand.productCount}
                        </Badge>
                      </CommandItem>
                    ))}
                  </ScrollArea>
                </CommandGroup>
              </CommandList>
            </Command>
          </div>
        </div>
      )}

      {/* Classes */}
      {filteredClasses.length > 0 && (
        <div>
          <Label className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3 block">
            Clase
          </Label>
          <div className="border rounded-md">
            <Command>
              <CommandInput placeholder="Cauta clasa..." />
              <CommandEmpty>Nu s-au gasit clase.</CommandEmpty>
              <CommandList>
                <CommandGroup>
                  <ScrollArea className="h-48">
                    {filteredClasses.map((cls) => (
                      <CommandItem
                        key={cls.id}
                        onSelect={() => handleClassToggle(cls.id)}
                        className="flex items-center space-x-2"
                      >
                        <Checkbox
                          checked={appliedFilters.classes?.includes(cls.name) || false}
                          onChange={() => handleClassToggle(cls.id)}
                        />
                        <span className="flex-1 text-sm">{cls.name}</span>
                        <Badge variant="secondary" className="text-xs">
                          {cls.productCount}
                        </Badge>
                      </CommandItem>
                    ))}
                  </ScrollArea>
                </CommandGroup>
              </CommandList>
            </Command>
          </div>
        </div>
      )}

      {/* Attributes */}
      {attributes.length > 0 && (
        <div>
          <Label className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3 block">
            Atribute
          </Label>
          <div className="space-y-4">
            {attributes.map((attribute) => (
              <div key={attribute.key} className="border rounded-md p-3">
                <h4 className="font-medium text-sm text-gray-900 dark:text-gray-100 mb-2">
                  {attribute.key}
                </h4>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {attribute.values.map((attrValue) => (
                    <div
                      key={attrValue.value}
                      className="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 p-1 rounded"
                      onClick={() => handleAttributeToggle(attribute.key, attrValue.value)}
                    >
                      <Checkbox
                        checked={
                          appliedFilters.attributes?.[attribute.key]?.includes(attrValue.value) || false
                        }
                        onCheckedChange={() => handleAttributeToggle(attribute.key, attrValue.value)}
                      />
                      <span className="flex-1 text-sm">{attrValue.value}</span>
                      <Badge variant="secondary" className="text-xs">
                        {attrValue.productCount}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {!has4th && (
        <>
          {/* Price Range */}
          <div>
            <Label className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3 block">
              Interval preturi
            </Label>
            <div className="space-y-4">
              <Slider
                value={priceValues}
                onValueChange={handlePriceChange}
                max={priceRange.max}
                min={priceRange.min}
                step={1}
                className="w-full"
              />
              <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                <span>{priceValues[0]} RON</span>
                <span>{priceValues[1]} RON</span>
              </div>
            </div>
          </div>

          {/* Discount Filter */}
          <div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="discount"
                checked={appliedFilters.hasDiscount || false}
                onCheckedChange={handleDiscountToggle}
              />
              <Label
                htmlFor="discount"
                className="text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer"
              >
                Doar produse cu reducere
              </Label>
            </div>
          </div>
        </>
      )}
    </div>
  )
}