// lib/prisma.ts
import { PrismaClient, Prisma } from "@/generated/prisma"
import { logger } from './logger'

const globalForPrisma = global as unknown as { prisma: PrismaClient }

const prismaClientSingleton = () => {
  const client = new PrismaClient({
    log: process.env.NODE_ENV === 'development'
      ? [{ level: 'query', emit: 'event' }, { level: 'warn', emit: 'stdout' }, { level: 'error', emit: 'stdout' }]
      : [{ level: 'error', emit: 'stdout' }],
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
  })

  if (process.env.NODE_ENV === 'development') {
    client.$on('query', (e: Prisma.QueryEvent) => {
      const truncatedQuery = e.query.length > 100 ? e.query.slice(0, 100) + '…' : e.query
      logger.debug(`🧵 [Prisma] Query (${e.duration}ms): ${truncatedQuery}`)
    })
  }

  return client
}

export const prisma = globalForPrisma.prisma || prismaClientSingleton()

if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.prisma = prisma
}

// Serverless-optimized retry logic
export async function withRetry<T>(
  fn: () => Promise<T>,
  { retries = 2, delay = 1000, backoff = 1.5 }: { retries?: number; delay?: number; backoff?: number } = {}
): Promise<T> {
  try {
    return await fn()
  } catch (error) {
    if (retries <= 0) throw error
    
    const isRetryableError = (error: unknown): boolean => {
      if (!(error instanceof Error)) return false;
      
      const retryableMessages = [
        'Connection pool',
        'Connection timeout',
        'ECONNREFUSED',
        'ECONNRESET',
        'ETIMEDOUT',
        'ETIMEOUT', 
        'socket hang up',
        'Connection lost',
        'Connection terminated unexpectedly',
        'Failed to connect',
        'Login timeout expired',
        'Connection is closed',
        'RequestError: Connection is closed', // MSSQL specific
        'TransactionError: Connection is closed', // MSSQL specific
        'ConnectionError', // MSSQL specific
      ];
      
      return retryableMessages.some(msg => error.message.includes(msg));
    };
    
    if (!isRetryableError(error)) throw error
    
    logger.warn(`Database operation failed, retrying (${retries} attempts left)...`, { error });
    
    await new Promise(resolve => setTimeout(resolve, delay))
    
    return withRetry(fn, { 
      retries: retries - 1, 
      delay: Math.min(delay * backoff, 3000), // Cap at 3 seconds for serverless
      backoff 
    })
  }
}

// Connection health check
export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    logger.error('Database health check failed:', error);
    return false;
  }
}

// Graceful shutdown (works in serverless too)
const handleShutdown = async () => {
  logger.info('Shutting down Prisma client...');
  try {
    await prisma.$disconnect();
  } catch (error) {
    logger.error('Error during Prisma disconnect:', error);
  }
};

// Only register shutdown handlers in development or traditional server environments
if (process.env.NODE_ENV === 'development') {
  process.on('SIGINT', handleShutdown);
  process.on('SIGTERM', handleShutdown);
  process.on('beforeExit', handleShutdown);
}


//to do on DB server : 
// # Install PgBouncer on your network
// sudo apt-get install pgbouncer

// # Configure /etc/pgbouncer/pgbouncer.ini
// [databases]
// your_db = host=localhost port=5432 dbname=your_actual_db

// [pgbouncer]
// listen_port = 6432
// listen_addr = *
// auth_type = md5
// auth_file = /etc/pgbouncer/userlist.txt
// pool_mode = transaction
// max_client_conn = 100
// default_pool_size = 20

// Update your postgresql.conf:

// # postgresql.conf
// listen_addresses = '*'
// max_connections = 100
// shared_preload_libraries = 'pg_stat_statements'

// Update pg_hba.conf:

// # pg_hba.conf - Be restrictive about who can connect
// host    all             all             0.0.0.0/0               md5
// # Or better yet, restrict to Vercel's IP ranges if possible



//ready for VPS hosting
// import { PrismaClient } from "@/generated/prisma"
// import { logger } from "./logger"

// const globalForPrisma = global as unknown as { prisma: PrismaClient }

// const prismaClientSingleton = () => {
//   const client = new PrismaClient({
//     log: process.env.NODE_ENV === 'development'
//       ? [{ level: 'query', emit: 'event' }, { level: 'warn', emit: 'stdout' }, { level: 'error', emit: 'stdout' }]
//       : [{ level: 'error', emit: 'stdout' }],
//   })

//   if (process.env.NODE_ENV === 'development') {
//     client.$on('query', (e) => {
//       const truncatedQuery = e.query.length > 100 ? e.query.slice(0, 100) + '…' : e.query
//       logger.warn(`🧵 [Prisma] Query (${e.duration}ms): ${truncatedQuery}`)
//     })
//   }

//   return client
// }

// export const prisma = globalForPrisma.prisma || prismaClientSingleton()

// if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma

// // Helper function with retry logic
// export async function withRetry<T>(
//   fn: () => Promise<T>,
//   { retries = 3, delay = 500, backoff = 2 }: { retries?: number; delay?: number; backoff?: number } = {}
// ): Promise<T> {
//   try {
//     return await fn()
//   } catch (error) {
//     if (retries <= 0) throw error
    
//     // Check if it's a connection error that we should retry
//     const isRetryableError = 
//       error instanceof Error && 
//       (error.message.includes('Connection pool') || 
//        error.message.includes('Connection timeout') ||
//       //  error.message.includes('P2021') || // Prisma-specific "Table does not exist" which can happen during cold starts
//       //  error.message.includes('P2002') || // Prisma-specific "Unique constraint failed" which can happen during cold starts
//        error.message.includes('P1001') || // Prisma-specific "Can't reach database server"
//        error.message.includes('P1012') || // Prisma-specific "Database dropped during query"
//        error.message.includes('P1013') || // Prisma-specific "Interactive transaction panic"
//        error.message.includes('P1014') || // Prisma-specific "Transaction failed due to a write conflict"
//        error.message.includes('P1015') || // Prisma-specific "Replication lag"
//        error.message.includes('P1016') || // Prisma-specific "Database schema changed during query"
//        error.message.includes('P1017') || // Prisma-specific "Connection to database lost during query"
//        error.message.includes('P1018') || // Prisma-specific "Serverless environment unhealthy"
//        error.message.includes('P1019') || // Prisma-specific "Query engine does not support queries"
//        error.message.includes('P1020') || // Prisma-specific "Query was interrupted"
//        error.message.includes('P1021') || // Prisma-specific "Query was cancelled"
//        error.message.includes('P1022') || // Prisma-specific "Query was timed out"
//        error.message.includes('did not initialize yet') ||
//        error.message.includes('ECONNREFUSED'));
    
//     if (!isRetryableError) throw error
    
//     logger.warn(`Database operation failed, retrying (${retries} attempts left)...`, error);
    
//     // Wait before retrying
//     await new Promise(resolve => setTimeout(resolve, delay))

//     //add delay
//     const nextDelay = Math.min(delay * backoff, 5000); // Cap at 5 seconds
    
//     // Retry with exponential backoff
//     return withRetry(fn, { 
//       retries: retries - 1, 
//       delay: nextDelay,
//       backoff 
//     })
//   }
// }

// // Graceful shutdown
// if (process.env.NODE_ENV === 'production') {
//   const handleShutdown = async () => {
//     logger.info('Shutting down Prisma client...');
//     await prisma.$disconnect();
//     process.exit(0);
//   };

//   process.on('SIGINT', handleShutdown);
//   process.on('SIGTERM', handleShutdown);
// }
