"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { UserX, Mail, Phone, ArrowLeft, AlertTriangle } from "lucide-react";
import Link from "next/link";


export default function AccountInactivePage() {

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-slate-50 flex items-center justify-center px-4">
      <div className="max-w-4xl mx-auto text-center">
        {/* Warning Icon */}
        <div className="mb-8">
          <div className="inline-flex items-center justify-center w-24 h-24 bg-orange-100 rounded-full mb-6 animate-pulse">
            <UserX className="w-12 h-12 text-orange-600" />
          </div>
        </div>

        {/* Status Message */}
        <div className="mb-12 space-y-4">
          <Badge className="bg-orange-100 text-orange-800 px-4 py-2 text-sm font-semibold mb-4">
            Account Inactive
          </Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-[#4D4D4D] mb-4">
            Account Temporarily Inactive
          </h1>
          <p className="text-xl text-slate-600 max-w-2xl mx-auto leading-relaxed">
            Your BMW parts account is currently inactive. This may be due to
            extended inactivity or pending verification.
          </p>
        </div>

        {/* Information Card */}
        <Card className="max-w-2xl mx-auto mb-8 shadow-xl border-0 bg-white">
          <CardContent className="p-8">
            <div className="space-y-6">
              <div className="flex items-center justify-center gap-3 mb-6">
                <AlertTriangle className="w-6 h-6 text-orange-600" />
                <h3 className="text-2xl font-bold text-[#4D4D4D]">
                  Account Status
                </h3>
              </div>

              <div className="text-left space-y-4">
                <div className="p-4 bg-orange-50 rounded-lg border border-orange-200">
                  <h4 className="font-semibold text-[#4D4D4D] mb-2">
                    Why is my account inactive?
                  </h4>
                  <ul className="text-sm text-slate-600 space-y-1">
                    <li>• Account hasn`t been used for an extended period</li>
                    <li>• Email verification may be required</li>
                    <li>• Account information needs to be updated</li>
                    <li>• Security verification pending</li>
                  </ul>
                </div>

                <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h4 className="font-semibold text-[#4D4D4D] mb-2">
                    How to reactivate?
                  </h4>
                  <ul className="text-sm text-slate-600 space-y-1">
                    <li>• Contact our customer support team</li>
                    <li>• Verify your identity and account information</li>
                    <li>• Update any outdated account details</li>
                    <li>• Complete any pending verification steps</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
          <Button
            onClick={() =>
              (window.location.href = "mailto:<EMAIL>")
            }
            className="bg-[#0066B1] hover:bg-[#0052A3] text-white px-8 py-3 text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
            size="lg"
          >
            <Mail className="w-5 h-5 mr-2" />
            Email Support
          </Button>

          <Button
            onClick={() => (window.location.href = "tel:******-BMW-PARTS")}
            variant="outline"
            className="border-[#0066B1] text-[#0066B1] hover:bg-[#0066B1] hover:text-white px-8 py-3 text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
            size="lg"
          >
            <Phone className="w-5 h-5 mr-2" />
            Call Support
          </Button>

          <Link href="/">
            <Button
              variant="ghost"
              className="text-[#4D4D4D] hover:text-[#0066B1] px-8 py-3 text-lg font-semibold transition-all duration-300"
              size="lg"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Return Home
            </Button>
          </Link>
        </div>

        {/* Support Information */}
        <div className="bg-white rounded-2xl shadow-xl p-8 max-w-3xl mx-auto">
          <h3 className="text-xl font-bold text-[#4D4D4D] mb-6">
            Customer Support
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-[#0066B1] rounded-full flex items-center justify-center mx-auto mb-3">
                <Mail className="w-6 h-6 text-white" />
              </div>
              <h4 className="font-semibold text-[#4D4D4D] mb-2">Email</h4>
              <p className="text-sm text-slate-600"><EMAIL></p>
              <p className="text-xs text-slate-500 mt-1">
                Response within 24 hours
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-[#0066B1] rounded-full flex items-center justify-center mx-auto mb-3">
                <Phone className="w-6 h-6 text-white" />
              </div>
              <h4 className="font-semibold text-[#4D4D4D] mb-2">Phone</h4>
              <p className="text-sm text-slate-600">1-800-BMW-PARTS</p>
              <p className="text-xs text-slate-500 mt-1">Mon-Fri 8AM-8PM EST</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-[#0066B1] rounded-full flex items-center justify-center mx-auto mb-3">
                <AlertTriangle className="w-6 h-6 text-white" />
              </div>
              <h4 className="font-semibold text-[#4D4D4D] mb-2">Priority</h4>
              <p className="text-sm text-slate-600">Account Issues</p>
              <p className="text-xs text-slate-500 mt-1">
                Fast-track resolution
              </p>
            </div>
          </div>
        </div>

        {/* Premium Badge */}
        <div className="mt-12">
          <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#0066B1] to-[#4D4D4D] rounded-full text-white font-semibold shadow-lg">
            <div className="w-2 h-2 bg-white rounded-full mr-3 animate-pulse"></div>
              Premium BMW Parts • Dedicated Support • Secure Platform
          </div>
        </div>
      </div>
    </div>
  );
}


// import { SignOutButton } from "@clerk/nextjs";
// import { Button } from "@/components/ui/button";
// import { getCurrentDbUser } from "@/lib/auth";
// import { redirect } from "next/navigation";

// export default async function AccountInactivePage() {
//     const user = await getCurrentDbUser()
  
//     if(!user){
//       redirect("sign-in")
//     }
//   return (
//     <div className="container mx-auto py-16 text-center">
//       <div className="max-w-md mx-auto p-8 rounded-lg shadow-md">
//         <h1 className="text-2xl font-bold text-amber-600 mb-4">Account Inactive</h1>
        
//         <p className="mb-6 text-gray-700">
//           Your account is currently inactive. This may be because your account
//           is new and pending approval, or it has been deactivated.
//         </p>
        
//         <p className="mb-8 text-gray-700">
//           If you believe this is an error, please contact our support team at 
//           <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
//             <EMAIL>
//           </a>
//         </p>
        
//         <SignOutButton>
//           <Button variant="outline" className="w-full">
//             Sign Out
//           </Button>
//         </SignOutButton>
//       </div>
//     </div>
//   );
// }