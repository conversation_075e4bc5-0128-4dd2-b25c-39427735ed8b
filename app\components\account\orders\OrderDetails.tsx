"use client"

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Truck, Package, CheckCircle, Clock, MapPin, Copy } from "lucide-react";
import { Order } from "@/types/orders";
import {
  getOrderStatusBadgeColor,
  formatCurrency,
  formatDate,
  formatDateTime,
  getStatusLabel,
  getShippingStatusColor
} from "@/lib/order-utils";
import { formatPriceRON } from "@/lib/utils";
import Image from "next/image";

interface OrderDetailsProps {
  order: Order;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  showShippingOnly?: boolean;
}

const OrderDetails = ({
  order,
  open,
  onOpenChange,
  showShippingOnly = false,
}: OrderDetailsProps) => {
  const copyTrackingNumber = () => {
    if (order.tracking) {
      navigator.clipboard.writeText(order.tracking);
    }
  };

  const getShippingStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "delivered":
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case "in transit":
      case "processing":
        return <Truck className="w-5 h-5 text-blue-600" />;
      case "shipped":
        return <Package className="w-5 h-5 text-orange-600" />;
      default:
        return <Clock className="w-5 h-5 text-gray-600" />;
    }
  };


  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto"
      >
        <DialogHeader>
          <DialogTitle>
            {showShippingOnly ? "Status livrare" : "Detalii comanda"}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {!showShippingOnly && (
            <>
              {/* Header Info */}
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-lg font-semibold">{order.orderNumber}</h3>
                  <p className="text-sm text-gray-500">
                    Comanda efectuata: {formatDate(order.date)}
                  </p>
                </div>
                <Badge className={getOrderStatusBadgeColor(order.orderStatus)}>
                  {order.status}
                </Badge>
              </div>

              {/* Products */}
              <div className="space-y-4">
                <h4 className="font-medium">Produse</h4>
                <div className="divide-y divide-gray-100">
                  {order.items.map((item, index) => (
                    <div key={index} className="py-4 flex gap-4">
                      <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0">
                        <Image
                          src={item.image}
                          alt={item.name}
                          className="w-full h-full object-cover"
                          width={100}
                          height={100}
                        />
                      </div>
                      <div className="flex-1">
                        <h5 className="font-medium">{item.name}</h5>
                        <p className="text-sm text-gray-500">
                          OE Cod: {item.oeCode}
                        </p>
                        <p className="text-sm text-gray-500">
                          Cantitate: {item.quantity}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">
                          {formatPriceRON(item.price)}
                        </p>
                        <p className="text-sm text-gray-500">
                          {formatPriceRON(item.price * item.quantity)} total
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}

          {/* Shipping Status Section */}
          {(showShippingOnly || !showShippingOnly) &&
            order.tracking && (
              <Card className="border-[#0066B1]/20">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Truck className="w-5 h-5 text-[#0066B1]" />
                    Status livrare
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getShippingStatusIcon(order.status)}
                      <div>
                        <Badge className={getShippingStatusColor(order.status)}>
                          {order.status}
                        </Badge>
                        {order.estimatedDelivery && (
                          <p className="text-sm text-gray-600 mt-1">
                            Est. livrare: {formatDate(order.estimatedDelivery)}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">Numar AWB</p>
                      <div className="flex items-center gap-2">
                        <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                          {order.tracking}
                        </code>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={copyTrackingNumber}
                          className="h-6 w-6 p-0"
                        >
                          <Copy className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </div>

                  {order.currentLocation && (
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <MapPin className="w-4 h-4" />
                      <span>
                        Locatie curenta: {order.currentLocation}
                      </span>
                    </div>
                  )}

                  {order.shippingHistory &&
                    order.shippingHistory.length > 0 && (
                      <div className="space-y-3">
                        <h5 className="font-medium text-sm">
                          Istoric livrare
                        </h5>
                        <div className="space-y-3">
                          {order.shippingHistory.map(
                            (event, index) => (
                              <div
                                key={index}
                                className="flex gap-3 pb-3 border-b border-gray-100 last:border-b-0"
                              >
                                <div className="flex-shrink-0 mt-1">
                                  <div className="w-2 h-2 bg-[#0066B1] rounded-full"></div>
                                </div>
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-center justify-between">
                                    <p className="text-sm font-medium">
                                      {event.status}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                      {formatDateTime(event.date)}
                                    </p>
                                  </div>
                                  <p className="text-sm text-gray-600">
                                    {event.location}
                                  </p>
                                  <p className="text-xs text-gray-500 mt-1">
                                    {event.description}
                                  </p>
                                </div>
                              </div>
                            ),
                          )}
                        </div>
                      </div>
                    )}
                </CardContent>
              </Card>
            )}

          {!showShippingOnly && (
            <>
              <div className="grid md:grid-cols-2 gap-6">
                {/* Billing Information */}
                {order.billing && (
                  <div className="space-y-2">
                    <h4 className="font-medium">Date facturare</h4>
                    <div className="bg-gray-50 rounded-lg p-4 text-sm">
                      <p className="font-medium">{order.billing.fullName}</p>
                      {order.billing.companyName && (
                        <p className="text-gray-600">{order.billing.companyName}</p>
                      )}
                      <p>{order.billing.address}</p>
                      <p>
                        {order.billing.city}, {order.billing.county}
                      </p>
                      {order.billing.cui && (
                        <p className="text-gray-600">CUI: {order.billing.cui}</p>
                      )}
                      <div className="mt-3 pt-3 border-t border-gray-200">
                        <p className="font-medium">Metoda de plata</p>
                        <p>{getStatusLabel("paymentMethod", order.paymentMethod)}</p>
                        {order.billing.iban && (
                          <p className="text-gray-600">IBAN: {order.billing.iban}</p>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {/* Shipping Information */}
                {order.shipping && (
                  <div className="space-y-2">
                    <h4 className="font-medium">Date livrare</h4>
                    <div className="bg-gray-50 rounded-lg p-4 text-sm">
                      <p className="font-medium">{order.shipping.fullName}</p>
                      <p>{order.shipping.address}</p>
                      <p>
                        {order.shipping.city}, {order.shipping.county}
                      </p>
                      <p className="text-gray-600">Telefon: {order.shipping.phoneNumber}</p>
                      {order.shipping.notes && (
                        <p className="text-gray-600">Nota: {order.shipping.notes}</p>
                      )}
                      <div className="mt-3 pt-3 border-t border-gray-200">
                        <p className="font-medium">Metoda livrare</p>
                        <p>{getStatusLabel("shippingMethod", order.shippingMethod)}</p>
                        {order.tracking && (
                          <p className="mt-1">
                            Numar AWB:{" "}
                            <span className="font-medium">
                              {order.tracking}
                            </span>
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Order Summary */}
              <div className="border-t border-gray-200 pt-4">
                <div className="flex justify-end">
                  <div className="w-64 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Subtotal</span>
                      <span>{formatCurrency(order.total)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Livrare</span>
                      <span>Gratuit</span>
                    </div>
                    <div className="flex justify-between font-medium text-lg pt-2 border-t border-gray-200">
                      <span>Total</span>
                      <span>{formatCurrency(order.total)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default OrderDetails;
