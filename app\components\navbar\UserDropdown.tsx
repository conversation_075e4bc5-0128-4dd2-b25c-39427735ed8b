"server-only"

import { User } from 'lucide-react';
import { AccountComponent } from './Account';
import { SignInButton, SignUpButton, SignedOut } from '@clerk/nextjs';
import { Button } from '@/components/ui/button';
import { User as PrismaUser } from '@/generated/prisma';

export async function UserDropdown( { user }: { user: PrismaUser | null }) {

  if (user) {
    const firstName = user.firstName || '';
    const email = user.email || '';
    const profileImage = user.profileImage || '';

    return <AccountComponent firstName={firstName} email={email} profileImage={profileImage}  />;
  }

  return (
    <div className="relative group">
      {/* Trigger */}
      <Button 
        variant="ghost"
        size="icon" 
        className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800"
      >
        <User className="h-6 w-6 text-[#4D4D4D] dark:text-gray-300" />
      </Button>

      {/* Dropdown for Signed Out Users */}
      <div className="absolute right-0 top-full mt-1 w-64 z-50 border border-gray-100 dark:border-gray-700 rounded-lg shadow-xl
         bg-white dark:bg-gray-800 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
        <div className="p-4 space-y-2">
          <SignedOut>
            <SignInButton mode="modal">
              <Button 
                className="w-full px-4 py-2 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600"
                variant="outline"
              >
                Conecteaza-te
              </Button>
            </SignInButton>
            <SignUpButton mode="modal">
              <Button 
                className="w-full px-4 py-2 bg-blue-300 rounded text-sm hover:bg-blue-400 text-gray-900"
                variant="outline"
              >
                Cont nou
              </Button>
            </SignUpButton>
          </SignedOut>
        </div>
      </div>
    </div>
  );
}
