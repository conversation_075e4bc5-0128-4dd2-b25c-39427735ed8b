import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON>le, CardDescription, CardContent } from "@/components/ui/card";

export default function ProfileLoading() {
  return (
    <div className="max-w-[1640px] mx-auto p-4 sm:p-6 lg:p-8">
      <div className="space-y-6">
        {/* Page Header */}
        <header className="flex justify-between items-center">
          <div>
            <Skeleton className="h-8 w-48 mb-1" />
            <Skeleton className="h-4 w-64" />
          </div>
        </header>

        {/* Profile Route Skeletons */}
        <div className="grid sm:grid-cols-1 md:grid-cols-2 gap-4">
          {/* Profile Image Upload Skeleton */}
          <Card>
            <CardHeader>
              <CardTitle>
                <Skeleton className="h-6 w-32" />
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                <Skeleton className="w-20 h-20 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-2/3" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
              </div>
              <div className="flex gap-2">
                <Skeleton className="h-10 w-32" />
                <Skeleton className="h-10 w-20" />
              </div>
            </CardContent>
          </Card>

          {/* Personal Info Card Skeleton */}
          <Card>
            <CardHeader>
              <CardTitle>
                <Skeleton className="h-6 w-48" />
              </CardTitle>
              <CardDescription>
                <Skeleton className="h-4 w-64 mt-2" />
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                {/* Name Fields Skeletons */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                </div>
                {/* Phone Number Skeleton */}
                <Skeleton className="h-10 w-full" />
                {/* Email Skeleton */}
                <Skeleton className="h-10 w-full" />
                {/* Submit Button Skeleton */}
                <div className="flex justify-end">
                  <Skeleton className="h-10 w-56" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

