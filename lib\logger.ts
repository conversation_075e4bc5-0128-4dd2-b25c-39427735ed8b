type LogLevel = 'debug' | 'info' | 'warn' | 'error';

// Define specific types for known properties
interface BaseLogOptions { 
  level?: LogLevel; 
  context?: string; 
  userId?: string;
  requestId?: string;
}

// Use a generic type for additional properties
type LogOptions = BaseLogOptions & Record<string, unknown>;

class Logger {
  private static instance: Logger;
  private readonly logLevel: LogLevel;
  private readonly isProduction: boolean;

  private constructor() {
    this.isProduction = process.env.NODE_ENV === 'production';
    // Set minimum log level for production
    this.logLevel = this.isProduction 
      ? (process.env.LOG_LEVEL as LogLevel) || 'info'
      : 'debug';
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  public debug(message: string, options?: LogOptions): void {
    if (this.shouldLog('debug')) {
      this.log('debug', message, options);
    }
  }

  public info(message: string, options?: LogOptions): void {
    if (this.shouldLog('info')) {
      this.log('info', message, options);
    }
  }

  public warn(message: string, options?: LogOptions): void {
    if (this.shouldLog('warn')) {
      this.log('warn', message, options);
    }
  }

  public error(message: string, error?: Error | unknown, options?: LogOptions): void {
    if (this.shouldLog('error')) {
      this.log('error', message, { 
        ...options, 
        error: this.formatError(error) 
      });
    }
  }

  // Add request context helper
  public withContext(context: Partial<LogOptions>) {
    return {
      debug: (message: string, options?: LogOptions) => 
        this.debug(message, { ...context, ...options }),
      info: (message: string, options?: LogOptions) => 
        this.info(message, { ...context, ...options }),
      warn: (message: string, options?: LogOptions) => 
        this.warn(message, { ...context, ...options }),
      error: (message: string, error?: Error | unknown, options?: LogOptions) => 
        this.error(message, error, { ...context, ...options }),
    };
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: LogLevel[] = ['debug', 'info', 'warn', 'error'];
    const currentLevelIndex = levels.indexOf(this.logLevel);
    const messageLevelIndex = levels.indexOf(level);
    return messageLevelIndex >= currentLevelIndex;
  }

  private log(level: LogLevel, message: string, options?: LogOptions): void {
    const timestamp = new Date().toISOString();
    const context = options?.context || 'app';

    if (this.isProduction) {
      // Optimized JSON format for Vercel
      const logData = {
        timestamp,
        level,
        message,
        context,
        ...(options?.userId && { userId: options.userId }),
        ...(options?.requestId && { requestId: options.requestId }),
        ...this.sanitizeOptions(options),
      };
      
      // Type assertion for console methods
      const consoleMethod = console[level] as (...args: unknown[]) => void;
      consoleMethod(JSON.stringify(logData));
    } else {
      // Readable format for development
      const extras = options ? ` ${JSON.stringify(options, null, 2)}` : '';
      const consoleMethod = console[level] as (...args: unknown[]) => void;
      consoleMethod(`[${timestamp}] [${level.toUpperCase()}] [${context}] ${message}${extras}`);
    }
  }

  private formatError(error?: Error | unknown): object | undefined {
    if (!error) return undefined;

    if (error instanceof Error) {
      return {
        name: error.name,
        message: error.message,
        stack: this.isProduction ? undefined : error.stack, // Hide stack in prod logs
        cause: error.cause,
      };
    }

    // Handle non-Error objects
    return {
      message: String(error),
      type: typeof error,
    };
  }

  private sanitizeOptions(options?: LogOptions): Record<string, unknown> {
    if (!options) return {};
    
    // Create a new object without sensitive keys
    const sanitized: Record<string, unknown> = {};
    const sensitiveKeys = ['level', 'context', 'error', 'password', 'token', 'secret'];
    
    for (const [key, value] of Object.entries(options)) {
      if (!sensitiveKeys.includes(key)) {
        sanitized[key] = value;
      }
    }
    
    return sanitized;
  }
}

export const logger = Logger.getInstance();

// type LogLevel = 'debug' | 'info' | 'warn' | 'error';

// interface LogOptions { 
//   level?: LogLevel; 
//   context?: string; 
//   userId?: string;
//   requestId?: string;
//   [key: string]: any; 
// }

// class Logger {
//   private static instance: Logger;
//   private readonly logLevel: LogLevel;
//   private readonly isProduction: boolean;

//   private constructor() {
//     this.isProduction = process.env.NODE_ENV === 'production';
//     // Set minimum log level for production
//     this.logLevel = this.isProduction 
//       ? (process.env.LOG_LEVEL as LogLevel) || 'info'
//       : 'debug';
//   }

//   public static getInstance(): Logger {
//     if (!Logger.instance) {
//       Logger.instance = new Logger();
//     }
//     return Logger.instance;
//   }

//   public debug(message: string, options?: LogOptions): void {
//     if (this.shouldLog('debug')) {
//       this.log('debug', message, options);
//     }
//   }

//   public info(message: string, options?: LogOptions): void {
//     if (this.shouldLog('info')) {
//       this.log('info', message, options);
//     }
//   }

//   public warn(message: string, options?: LogOptions): void {
//     if (this.shouldLog('warn')) {
//       this.log('warn', message, options);
//     }
//   }

//   public error(message: string, error?: Error | unknown, options?: LogOptions): void {
//     if (this.shouldLog('error')) {
//       this.log('error', message, { 
//         ...options, 
//         error: this.formatError(error) 
//       });
//     }
//   }

//   // Add request context helper
//   public withContext(context: Partial<LogOptions>) {
//     return {
//       debug: (message: string, options?: LogOptions) => 
//         this.debug(message, { ...context, ...options }),
//       info: (message: string, options?: LogOptions) => 
//         this.info(message, { ...context, ...options }),
//       warn: (message: string, options?: LogOptions) => 
//         this.warn(message, { ...context, ...options }),
//       error: (message: string, error?: Error | unknown, options?: LogOptions) => 
//         this.error(message, error, { ...context, ...options }),
//     };
//   }

//   private shouldLog(level: LogLevel): boolean {
//     const levels = ['debug', 'info', 'warn', 'error'];
//     const currentLevelIndex = levels.indexOf(this.logLevel);
//     const messageLevelIndex = levels.indexOf(level);
//     return messageLevelIndex >= currentLevelIndex;
//   }

//   private log(level: LogLevel, message: string, options?: LogOptions): void {
//     const timestamp = new Date().toISOString();
//     const context = options?.context || 'app';

//     if (this.isProduction) {
//       // Optimized JSON format for Vercel
//       const logData = {
//         timestamp,
//         level,
//         message,
//         context,
//         ...(options?.userId && { userId: options.userId }),
//         ...(options?.requestId && { requestId: options.requestId }),
//         ...this.sanitizeOptions(options),
//       };
      
//       // Vercel captures this automatically
//       console[level](JSON.stringify(logData));
//     } else {
//       // Readable format for development
//       const extras = options ? ` ${JSON.stringify(options, null, 2)}` : '';
//       console[level](`[${timestamp}] [${level.toUpperCase()}] [${context}] ${message}${extras}`);
//     }
//   }

//   private formatError(error?: Error | unknown): object | undefined {
//     if (!error) return undefined;

//     if (error instanceof Error) {
//       return {
//         name: error.name,
//         message: error.message,
//         stack: this.isProduction ? undefined : error.stack, // Hide stack in prod logs
//         cause: error.cause,
//       };
//     }

//     // Handle non-Error objects
//     return {
//       message: String(error),
//       type: typeof error,
//     };
//   }

//   private sanitizeOptions(options?: LogOptions): object {
//     if (!options) return {};
    
//     // Remove internal properties and sensitive data
//     const { level, context, error, password, token, secret, ...safe } = options;
//     return safe;
//   }
// }

// export const logger = Logger.getInstance();


//simpler logger
// type LogLevel = 'debug' | 'info' | 'warn' | 'error';

// interface LogOptions {
//   level?: LogLevel;
//   context?: string;
//   [key: string]: any;
// }

// class Logger {
//   private static instance: Logger;
  
//   private constructor() {}
  
//   public static getInstance(): Logger {
//     if (!Logger.instance) {
//       Logger.instance = new Logger();
//     }
//     return Logger.instance;
//   }
  
//   public debug(message: string, options?: LogOptions): void {
//     this.log('debug', message, options);
//   }
  
//   public info(message: string, options?: LogOptions): void {
//     this.log('info', message, options);
//   }
  
//   public warn(message: string, options?: LogOptions): void {
//     this.log('warn', message, options);
//   }
  
//   public error(message: string, error?: Error, options?: LogOptions): void {
//     this.log('error', message, { ...options, error: this.formatError(error) });
//   }
  
//   private log(level: LogLevel, message: string, options?: LogOptions): void {
//     const timestamp = new Date().toISOString();
//     const context = options?.context || 'app';
    
//     // In production, you might want to use a proper logging service
//     if (process.env.NODE_ENV === 'production') {
//       // Format for structured logging
//       const logData = {
//         timestamp,
//         level,
//         message,
//         context,
//         ...options,
//       };
      
//       // In production, you might send this to a logging service
//       console[level](JSON.stringify(logData));
//     } else {
//       // More readable format for development
//       const extras = options ? ` ${JSON.stringify(options)}` : '';
//       console[level](`[${timestamp}] [${level.toUpperCase()}] [${context}] ${message}${extras}`);
//     }
//   }
  
//   private formatError(error?: Error): object | undefined {
//     if (!error) return undefined;
    
//     return {
//       name: error.name,
//       message: error.message,
//       stack: error.stack,
//       cause: error.cause,
//     };
//   }
// }

// export const logger = Logger.getInstance();