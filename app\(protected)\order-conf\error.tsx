// app/(protected)/order-conf/error.tsx
'use client'

import { useEffect } from 'react'

export default function OrderConfError({
  error,
}: {
  error: Error & { digest?: string }
}) {
  useEffect(() => {
    // Log to monitoring service
    console.error('Error in order confirmation route:', error)
  }, [error])

  return (
    <div className="min-h-screen flex items-center justify-center p-4"> </div>
    )
}