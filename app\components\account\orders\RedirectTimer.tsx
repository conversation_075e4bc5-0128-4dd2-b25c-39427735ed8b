// app/components/RedirectTimer.tsx

"use client"; // This is the most important line!

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export function RedirectTimer({ redirectTo = '/', delay = 10000 }: { redirectTo?: string; delay?: number }) {
  const router = useRouter();

  useEffect(() => {
    // Set up the timer
    const timerId = setTimeout(() => {
      // Redirect after the delay
      router.push(redirectTo);
    }, delay);

    // Clean up the timer if the component unmounts before the time is up
    return () => clearTimeout(timerId);

  }, [router, redirectTo, delay]); // Dependencies for the effect

  // This component doesn't need to render anything visible
  return null;
}