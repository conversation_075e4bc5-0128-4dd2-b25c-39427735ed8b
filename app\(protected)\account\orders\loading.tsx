//loading component for account/orders route with skeleton based on the page.tsx


// export default function OrdersRouteSkeleton() {
//   return (
//     <div className="min-h-screen">  
//       <div className="max-w-[1640px] mx-auto px-4 py-6">
//         <div className="flex justify-between items-center mb-6">
//           <h1 className="text-2xl font-semibold">Comenzile mele</h1>
//         </div>
//         <div className="space-y-4">
//           <div className="flex items-center justify-center py-8">
//             <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#0066B1]"></div>
//             <span className="ml-2 text-gray-600">Se incarca...</span>
//           </div>
//         </div>
//       </div></div>  
//       );
// }

// app/orders/loading.tsx
import { Skeleton } from "@/components/ui/skeleton";
import { Search } from "lucide-react";

export default function OrdersLoading() {
  return (
    <div className="max-w-[1640px] mx-auto p-4 sm:p-6 lg:p-8 flex flex-col items-center justify-center rounded-lg text-center">
      {/* Title */}
      <div className="flex justify-between items-center w-full mb-6">
        <Skeleton className="h-8 w-48" />
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6 w-full">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Skeleton className="h-10 w-full pl-10" />
        </div>
        <Skeleton className="h-10 w-32" />
        <Skeleton className="h-10 w-32" />
        <div className="flex gap-2">
          <Skeleton className="h-10 w-48" />
        </div>
      </div>

      {/* Orders list placeholder */}
      <div className="space-y-4 w-full">
        {Array.from({ length: 3 }).map((_, idx) => (
          <div
            key={idx}
            className="border rounded-lg shadow p-6 hover:border-[#0066B1] transition-colors"
          >
            <div className="flex flex-wrap gap-4 justify-between items-start mb-3">
              <div>
                <Skeleton className="h-5 w-24 mb-1" />
                <Skeleton className="h-4 w-16" />
              </div>
              <Skeleton className="h-6 w-20" />
            </div>

            <div className="space-y-2 mb-3">
              {Array.from({ length: 2 }).map((_, i) => (
                <div key={i} className="flex justify-between items-center">
                  <div>
                    <Skeleton className="h-4 w-32 mb-1" />
                    <Skeleton className="h-3 w-24" />
                  </div>
                </div>
              ))}
              <Skeleton className="h-3 w-16 ml-auto" />
            </div>

            <div className="pt-3 border-t border-gray-100 flex flex-wrap items-center justify-between gap-2">
              <Skeleton className="h-6 w-24" />
              <div className="flex gap-1">
                <Skeleton className="h-7 w-20" />
                <Skeleton className="h-7 w-20" />
                <Skeleton className="h-7 w-20" />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      <div className="mt-8 flex justify-center space-x-2">
        <Skeleton className="h-8 w-24" />
        {Array.from({ length: 3 }).map((_, i) => (
          <Skeleton key={i} className="h-8 w-12" />
        ))}
        <Skeleton className="h-8 w-24" />
      </div>
    </div>
  );
}
