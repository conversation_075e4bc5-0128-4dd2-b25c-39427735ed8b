'use client';

import { useUser } from '@clerk/nextjs';
import { useEffect, useState } from 'react';

// Assuming you have shadcn components for Button, Checkbox, Select, etc.
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

export function CustomPreferencesForm() {
  const { user } = useUser();
  const [titulatura, setTitulatura] = useState('');
  const [newsletter, setNewsletter] = useState(false);
  const [marketing, setMarketing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form state from user's metadata
  useEffect(() => {
    if (user?.unsafeMetadata) {
      setTitulatura((user.unsafeMetadata.titulatura as string) || '');
      setNewsletter(!!user.unsafeMetadata.newsletter);
      setMarketing(!!user.unsafeMetadata.marketing);
    }
  }, [user]);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsSubmitting(true);
    //const formData = new FormData(event.currentTarget);
    const result = true
    if (result) {
      alert(result,); // Or show a toast notification
    } else {
      alert(result); // Or show a success toast
    }
    setIsSubmitting(false);
  };

  return (
    <div className="max-w-md">
      <h2 className="text-xl font-semibold mb-4">Preferinte</h2>
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <Label htmlFor="titulatura">Titulatură</Label>
          {/* We need to use a key on Select for it to re-render correctly with a new default value */}
          <Select
            name="titulatura"
            value={titulatura}
            onValueChange={setTitulatura}
            key={titulatura}
          >
            <SelectTrigger id="titulatura">
              <SelectValue placeholder="Selectează o opțiune" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="dl">Domnul (Dl.)</SelectItem>
              <SelectItem value="dna">Doamna (Dna.)</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-center space-x-2">
          <Checkbox
            id="newsletter"
            name="newsletter"
            checked={newsletter}
            onCheckedChange={(checked) => setNewsletter(!!checked)}
          />
          <Label htmlFor="newsletter">
            Doresc să primesc newsletter-ul săptămânal
          </Label>
        </div>
        <div className="flex items-center space-x-2">
          <Checkbox
            id="marketing"
            name="marketing"
            checked={marketing}
            onCheckedChange={(checked) => setMarketing(!!checked)}
          />
          <Label htmlFor="marketing">
            Sunt de acord cu primirea de oferte de marketing
          </Label>
        </div>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? 'Se salveaza...' : 'Salveaza Preferintele'}
        </Button>
      </form>
    </div>
  );
}