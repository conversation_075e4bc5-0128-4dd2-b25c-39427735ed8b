import { cookies } from "next/headers";

const LOGIN_COOKIE_PREFIX = "login_ts_";
const LOGIN_COOKIE_MAX_AGE = 60 * 60 * 2; // 2 hours

export async function shouldUpdateLoginStats(userId?: string): Promise<boolean> {
  const cookieStore = await cookies();
  const cookieName = userId ? `${LOGIN_COOKIE_PREFIX}${userId}` : "last_login_timestamp";
  const lastLoginTimestamp = cookieStore.get(cookieName);
  
  const now = new Date();
  const updateNeeded = !lastLoginTimestamp ||
    now.getTime() - new Date(lastLoginTimestamp.value).getTime() > LOGIN_COOKIE_MAX_AGE * 1000;

  if (updateNeeded) {
    await setLoginTimestamp(userId);
  }

  return updateNeeded;
}

async function setLoginTimestamp(userId?: string): Promise<void> {
  const cookieStore = await cookies();
  const cookieName = userId ? `${LOGIN_COOKIE_PREFIX}${userId}` : "last_login_timestamp";
  
  cookieStore.set(cookieName, new Date().toISOString(), {
    maxAge: LOGIN_COOKIE_MAX_AGE,
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "lax",
    path: "/",
  });
}