import type { DiscountType as PrismaDiscountType } from "@/generated/prisma";

export interface WishlistItems {
    product: {
        Material_Number: string;
        Description_Local: string | null;
        ImageUrl: string[];
        FinalPrice: number | null;
        PretAM: number | null;
        HasDiscount: boolean;
        activeDiscountType: PrismaDiscountType | null;
        activeDiscountValue: number | null;
        discountPercentage: number | null;
    };
}


export interface EnrichedWishlistItem extends WishlistItems {
  stock: number;
  displayPrice: number | null;
}