"server-only"

import { Decimal } from "@/generated/prisma/runtime/library";
import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function getClientIp(req: Request): string | null {
  const forwarded = req.headers.get('x-forwarded-for');
  if (forwarded) return forwarded.split(',')[0].trim();
  return req.headers.get('x-real-ip');
}

export  function getStockStatus(stock?: number) {
      if (stock === undefined) return "UNKNOWN";
      if (stock > 5) return "in stoc";
      if (stock > 0) return "stoc critic";
      return "stoc epuizat";
}

interface StockStatus {
  statusText: string;
  dotColorClass: string;
}

//function for formating the data, RO
export function formatDate(dateString: string, options?: Intl.DateTimeFormatOptions): string {
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  };
  
  return new Date(dateString).toLocaleDateString("ro-RO", options || defaultOptions);
}

export function getStockStatusData(stock: number | null | undefined): StockStatus {
  // Handle cases where stock might be null or undefined
  if (stock === null || stock === undefined || stock <= 0) {
    return { statusText: "stoc epuizat", dotColorClass: "bg-red-500" };
  } else if (stock > 0 && stock <= 10) { // Example: 1 to 10 units
    return { statusText: "stoc critic", dotColorClass: "bg-orange-500" };
  } else { // Example: More than 10 units
    return { statusText: "in stoc", dotColorClass: "bg-green-500" };
  }
}

interface LocationDetails {
  name: string;
  address: string;
}

// A map from your location codes to their display names and addresses
export const locationDetailsMap: Record<string, LocationDetails> = {
  'BAN': { name: 'Baneasa', address: 'Str. George Bacovia 1, Bucuresti, Romania' },
  'OTP': { name: 'Otopeni', address: 'Calea Bucureștilor 224, Otopeni, Romania' },
  'ACJ': { name: 'Cluj', address: 'Str. Fabricii 25, Cluj-Napoca, Romania' },
  'MIL': { name: 'Militari', address: 'Bd. Iuliu Maniu 7, Bucuresti, Romania' },
  'BCT': { name: 'Constanta', address: 'Str. Zizinului 3, Brasov, Romania' },
  'ABC': { name: 'Bacau', address: 'Str. Ardealului 10, Alba Iulia, Romania' },
  'CSB': { name: 'Sibiu', address: 'Bd. Tomis 500, Constanta, Romania' },
  'BAR': { name: 'Arad', address: 'Str. Republicii 12, Bacau, Romania' },
  'BTM': { name: 'Timisoara', address: 'Calea Nationala 8, Botosani, Romania' },
  'CBV': { name: 'Brasov', address: 'Calea Severinului 100, Craiova, Romania' },
  'CRA': { name: 'Craiova', address: 'Bd. Carol I 20, Craiova, Romania' },
  'JIL': { name: 'Jilava', address: 'Str. Principală 150, Jilava, Romania' },
  'MTG': { name: 'Targu Mures', address: 'Str. Uzinei 1, Mioveni, Romania' },
  // Fallback for any unexpected or unknown location codes
  'UNKNOWN': { name: 'Unknown Location', address: 'Address Not Available' },
};

// Helper function to safely get location details
export function getLocationDetails(code: string): LocationDetails {
  return locationDetailsMap[code] || locationDetailsMap['UNKNOWN'];
}

export function formatPriceRON(price: number | null): string {
  if (typeof price !== "number") return "";

  const formatted = new Intl.NumberFormat("ro-RO", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(price);

  return `${formatted} Lei`; // Capitalized
}

export function formatDiscount(
  type: "PERCENTAGE" | "FIXED_AMOUNT" | "NEW_PRICE" | null,
  value: number | null
): string | null {
  if (type === null || value === null) return null;

  const formatted = value.toLocaleString("ro-RO", { minimumFractionDigits: 2 });

  switch (type) {
    case "PERCENTAGE":
      return `-${formatted}%`;
    case "FIXED_AMOUNT":
      return `-${formatted} Lei`;
    case "NEW_PRICE":
      return `${formatted} Lei`;
    default:
      return null;
  }
}

/**
 * Converts a value to a number safely, handling Prisma Decimal objects
 * @param x - The value to convert to a number
 * @returns A number, or null if conversion is not possible
 */
export function toSafeNumber(x: unknown): number | null {
  if (x == null) return null;
  
  // Handle Prisma Decimal instance
  if (isPrismaDecimal(x)) {
    return x.toNumber();
  }
  
  // Handle string or number
  const n = Number(x);
  return Number.isNaN(n) ? null : n;
}

/**
 * Type guard to check if a value is a Prisma Decimal
 */
function isPrismaDecimal(value: unknown): value is Decimal {
  return (
    typeof value === 'object' && 
    value !== null && 
    'toNumber' in value && 
    typeof (value as Decimal).toNumber === 'function'
  );
}

// export function toSafeNumber(
//   x: unknown
// ): number | null {
//   if (x == null) return null;
//   // Prisma Decimal instance
//   if (typeof x === "object" && typeof (x as any).toNumber === "function") {
//     return (x as any).toNumber();
//   }
//   // Already a string or number
//   const n = Number(x);
//   return Number.isNaN(n) ? null : n;
// }

export const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

const UNIT_TRANSLATIONS: Record<string,string> = {
  PCE:   "Bucata",
  PK:    "Pachet",
  MTR:   "Metru",
  PA:    "Set",
  LTR:   "Litru",
  GRM:   "Gram",
  CM:    "Centimetru",
  SQM:   "Metru Patrat",
  SHEET: "Foaie",
};

export function translateUnit(unit: string): string {
  return UNIT_TRANSLATIONS[unit] ?? unit;
}

  // Format phone number to E.164
export const formatPhoneNumber = (phone: string): string => {
    if (!phone) return '';
    
    // Remove all non-digit characters
    const digits = phone.replace(/\D/g, '');
    
    // If starts with 0 (Romanian format), replace with +40
    if (digits.startsWith('0')) {
      return `+40${digits.substring(1)}`;
    }
    
    // If doesn't start with +, assume Romanian and add +40
    if (!phone.startsWith('+')) {
      return `+40${digits}`;
    }
    
    return phone;
  };

// Validation functions
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 255;
}

export function isValidName(name: string | null): boolean {
  if (!name) return true; // Allow empty names
  return name.length <= 100 && !/[<>{}]/.test(name); // Basic XSS protection
}

export function isValidImageUrl(url: string | null): boolean {
  if (!url) return true; // Allow empty URLs
  try {
    const parsedUrl = new URL(url);
    return ['http:', 'https:'].includes(parsedUrl.protocol) && url.length <= 500;
  } catch {
    return false;
  }
}

export function maskEmail(email: string): string {
  const [local, domain] = email.split('@');
  if (!local || !domain) return email; // Fallback for invalid email format
  
  const maskedLocal = local.length > 2 ? 
    local.substring(0, 2) + '*'.repeat(local.length - 2) : 
    local;
  return `${maskedLocal}@${domain}`;
}