import { Skeleton } from "@/components/ui/skeleton";

export default function WishlistRoute() {

    return (
      <div className="max-w-[1640px] mx-auto p-4">
        {/* Title */}
        <Skeleton className="h-8 w-1/3 mb-6" />

        {/* Placeholder box */}
        <div className="border rounded-lg overflow-hidden shadow space-y-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <div
              key={i}
              className="flex items-center gap-6 p-6 border-b last:border-0"
            >
              {/* Image */}
              <Skeleton className="w-24 h-24 rounded-lg" />

              {/* Details */}
              <div className="flex-1 space-y-2">
                <Skeleton className="h-5 w-1/2" />
                <Skeleton className="h-4 w-1/4" />
                <Skeleton className="h-5 w-1/6" />
              </div>

              {/* Price & actions */}
              <div className="flex flex-col items-end space-y-2">
                <Skeleton className="h-4 w-12" />
                <Skeleton className="h-6 w-16" />
                <div className="flex space-x-2">
                  <Skeleton className="h-8 w-8 rounded" />
                  <Skeleton className="h-8 w-8 rounded" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
}