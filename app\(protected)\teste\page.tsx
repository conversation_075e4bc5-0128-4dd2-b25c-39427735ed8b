// app/dashboard/devices/page.tsx

import { auth, clerkClient } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
//import UAParser from 'ua-parser-js';

// This is a Server Component, so we can make it async
export default async function DevicesPage() {
  // 1. Get the user ID from the session.
  const { userId } = await auth();

  // If there's no user, redirect them to the sign-in page.
  if (!userId) {
    redirect('/sign-in');
  }

  // 2. Fetch all sessions for the user using the Backend API.
  const client = await clerkClient()
   const sessions = await client.sessions.getSessionList({userId});
  const activeSessions = sessions.data.filter((session) => session.status === 'active');
console.log(activeSessions.map(session => session.id));
//   if (activeSessions.length === 0) {
//     return (
//       <div>
//         <h2>Your Connected Devices</h2>
//         <p>You are not signed in on any other devices.</p>
//       </div>
//     );
//   }

return (<>

asdasd</>)

  // 3. Process and parse the session data on the server.
//   const processedSessions = activeSessions.map((session) => {
//     const parser = new UAParser(session.userAgent || '');
//     const result = parser.getResult();

//     return {
//       id: session.id,
//       browser: `${result.browser.name || 'Unknown'} ${result.browser.version || ''}`.trim(),
//       os: `${result.os.name || 'Unknown'} ${result.os.version || ''}`.trim(),
//       device: result.device.type ? result.device.type.charAt(0).toUpperCase() + result.device.type.slice(1) : 'Desktop',
//       ipAddress: session.ipAddress,
//       lastActiveAt: new Date(session.lastActiveAt).toLocaleString(),
//       isCurrent: session.id === auth().sessionId // Check if this is the current session
//     };
//   });

//   return (
//     <div>
//       <h2>Your Connected Devices</h2>
//       <p>This is a list of devices that have an active session with your account.</p>
      
//       <div style={{ marginTop: '2rem' }}>
//         {processedSessions.map((s) => (
//           <div key={s.id} style={{ border: '1px solid #e0e0e0', padding: '16px', borderRadius: '8px', marginBottom: '16px' }}>
//             <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
//               <div>
//                 <p style={{ margin: '0 0 4px 0' }}>
//                   <strong>{s.os}</strong>
//                   {s.isCurrent && <span style={{ marginLeft: '8px', background: '#e0f2fe', color: '#0ea5e9', padding: '2px 6px', borderRadius: '4px', fontSize: '0.8rem' }}>Current device</span>}
//                 </p>
//                 <p style={{ margin: 0, color: '#666' }}>
//                   {s.browser} • {s.ipAddress}
//                 </p>
//               </div>
//               <div>
//                 {/* Note: Revoking a session requires a client-side action. See below. */}
//               </div>
//             </div>
//             <p style={{ margin: '8px 0 0 0', fontSize: '0.9rem', color: '#666' }}>
//               Last active: {s.lastActiveAt}
//             </p>
//           </div>
//         ))}
//       </div>
//     </div>
//   );
}