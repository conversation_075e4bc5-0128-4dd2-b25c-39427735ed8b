"server-only"

import type { <PERSON>ada<PERSON> } from "next";
import "../globals.css";
import { Clerk<PERSON>rovider } from "@clerk/nextjs";
import { getCurrentDbUser } from "@/lib/auth";
import { getWishlistProductCodes } from "../getData/wishlist";
import { Providers } from "../providers";
import { WishlistProvider } from "../context/WishlistContext";
import { MainNavbar } from "../components/navbar/MainNavbar";
import MegaMenuAndCategory from "../components/navbar/MegaMenuCategory";
import Footer from "../components/footer/Footer";
import { Toaster } from "@/components/ui/sonner";
import { getCart } from "../getData/cart";
import { Cart, CartItem } from "@/types/cart";

export const metadata: Metadata = {
  title: "Automobile Bavaria : Piese auto BMW originale",
  description: "Piese auto BMW originale",
};


export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {

 // Fetch user once at the root level
  const user = await getCurrentDbUser();

  let wishlistProductCodes = new Set<string>();
  let cart: Cart | null = null;

  if (user) {
    // Fetch both wishlist and cart data in parallel
    const [wishlistData, cartData] = await Promise.all([
      getWishlistProductCodes(user.id),
      getCart(user.id)
    ]);
    
    wishlistProductCodes = wishlistData;
    cart = cartData;
  }

  const cartItems: CartItem[] = cart?.items.filter((item) => item.quantity > 0) || [];

  return (
    <ClerkProvider>
          <Providers>
            <WishlistProvider initialItems={wishlistProductCodes}>
                <div className="relative w-full shadow-md">
                  <div className="max-w-[1640px] mx-auto">
                    <MainNavbar  
                      user={user}
                      cartItems={cartItems}
                    />
                    <MegaMenuAndCategory />
                  </div>
                </div>
                <div className="relative w-full">
                  {children}
                  <Footer />
                </div>
                <Toaster />
            </WishlistProvider>
          </Providers>
    </ClerkProvider>
  );
}


