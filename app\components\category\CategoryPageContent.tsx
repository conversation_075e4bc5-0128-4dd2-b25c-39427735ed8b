import { SearchPageData } from "@/types/search"
import CategoryFilters from "./CategoryFilters"
import CategoryHeader from "./CategoryHeader"
import SearchResults from "../search/SearchResults"
import SearchPagination from "../search/SearchPagination"

interface CategoryPageContentProps {
  has4th: boolean
  searchData: SearchPageData
  categoryName: string
}

export default function CategoryPageContent({ searchData, categoryName, has4th }: CategoryPageContentProps) {
  const { products, categories, brands, classes, attributes, priceRange, pagination, appliedFilters } = searchData

  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
      {/* Left Sidebar - Filters */}
      <div className="lg:col-span-1">
        <CategoryFilters
          has4th={has4th}
          categories={categories}
          brands={brands}
          classes={classes}
          attributes={attributes}
          priceRange={priceRange}
          appliedFilters={appliedFilters}
        />
      </div>

      {/* Right Content - Results */}
      <div className="lg:col-span-3">
        <CategoryHeader
          appliedFilters={appliedFilters}
          totalProducts={pagination.total}
          categoryName={categoryName}
          searchData={searchData}
        />
        
        <SearchResults products={products} />
        
        {pagination.pages > 1 && (
          <div className="mt-8">
            <SearchPagination pagination={pagination} />
          </div>
        )}
      </div>
    </div>
  )
}
