export interface DbUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  profileImage: string;
  externalId: string;
  externalProvider: string;
  lastLoginAt: string | null;
  loginCount: number;
  lastActivityAt: string | null;
  createdAt: string;
  updatedAt: string;
  role?: string;
  isActive?: boolean;
  isSuspended?: boolean;
  suspensionReason?: string | null;
  deletedAt?: string | null;
  // Add other fields as needed
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface UserApiResponse {
  clerkUser: {
    id: string;
    firstName: string | null;
    lastName: string | null;
    emailAddress: string | null;
    imageUrl: string;
  };
  dbUser: DbUser;
}