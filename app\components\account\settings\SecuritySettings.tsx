"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Shield,
  Smartphone,
  AlertTriangle,
  MonitorSmartphone
} from "lucide-react";
import { UserSecurityInfo, UserDevice } from "@/app/getData/account-settings";
import MFADialog from "./manage-mfa";
import { useRouter } from "next/navigation";
import UserDevices from "./UserDevices";
import DeleteAccount from "./DeleteAccount";

interface SecuritySettingsProps {
  securityInfo: UserSecurityInfo | null;
  ssoProvider?: string | null;
  devices: UserDevice[];
}

export default function SecuritySettings({ securityInfo, ssoProvider, devices }: SecuritySettingsProps) {
  const [mfaDialogOpen, setMfaDialogOpen] = useState(false);
  const [currentMfaStatus, setCurrentMfaStatus] = useState(securityInfo?.twoFactorEnabled || false);
  const router = useRouter();

  return (
    <div className="space-y-6">
      {/* Security Settings Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Setari Securitate
          </CardTitle>
          <CardDescription>
            Gestionati setarile de securitate pentru a va proteja contul
          </CardDescription>
        </CardHeader>
        <CardContent>
          
            {/* Two-Factor Authentication */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Smartphone className="h-5 w-5 text-muted-foreground" />
                  <div className="space-y-1">
                    <Label htmlFor="twoFactorEnabled" className="text-base font-medium">
                      Autentificare cu doi factori (2FA)
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      {ssoProvider
                        ? `2FA este gestionat de ${ssoProvider === 'oauth_microsoft' ? 'Microsoft' : ssoProvider === 'oauth_google' ? 'Google' : ssoProvider === 'oauth_facebook' ? 'Facebook' : ssoProvider }`
                        : "Adaugati un nivel suplimentar de securitate la contul dvs."
                      }
                    </p>
                  </div>
                </div>
                {ssoProvider ? (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const urls = {
                        google: 'https://myaccount.google.com/security',
                        microsoft: 'https://account.microsoft.com/security',
                        facebook: 'https://www.facebook.com/settings?tab=security',
                      };
                      window.open(urls[ssoProvider as keyof typeof urls] || '#', '_blank');
                    }}
                  >
                    Gestioneaza in {ssoProvider === 'microsoft' ? 'Microsoft' : ssoProvider === 'google' ? 'Google' : ssoProvider === 'facebook' ? 'Facebook' : ssoProvider}
                  </Button>
                ) : (
                  <Button
                    type="submit" 
                    //disabled={isPending || !isDirty}
                    className="bg-[#0066B1] hover:bg-[#004d85] text-white"
                    onClick={() => setMfaDialogOpen(true)}
                  >
                    Gestioneaza 2FA
                  </Button>
                )}
              </div>

              {currentMfaStatus && (
                <Alert>
                  <Smartphone className="h-4 w-4" />
                  <AlertDescription>
                    <strong>2FA Activat:</strong> Contul dvs. este protejat cu autentificare cu doi factori.
                    Veti fi solicitat sa introduceti un cod de verificare la fiecare autentificare.
                  </AlertDescription>
                </Alert>
              )}

              {!currentMfaStatus && !ssoProvider && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Recomandare de securitate:</strong> Activati autentificarea cu doi factori
                    pentru a va proteja contul impotriva accesului neautorizat.
                  </AlertDescription>
                </Alert>
              )}
            </div>

        </CardContent>
      </Card>

      {/* Lock Account */}
      <DeleteAccount />


      {/* Devices */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MonitorSmartphone className="h-5 w-5" />
            Dispozitive active
          </CardTitle>
          <CardDescription>
            Gestionati dispozitivele folosite pentru autentificare
          </CardDescription>
        </CardHeader>
        <CardContent>
           <UserDevices  devicesProp={devices} />
        </CardContent>
      </Card>

      {/* MFA Management Dialog */}
      <MFADialog
        open={mfaDialogOpen}
        onOpenChange={setMfaDialogOpen}
        onMFAStatusChange={(enabled) => {
          setCurrentMfaStatus(enabled);
          //refresh page with router
          router.refresh();
          
        }}
      />
    </div>

  );
}
