"use client"

import React, { useState, useEffect, useRef, useCallback } from "react";
import { Search, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { formatPriceRON, formatDiscount } from "@/lib/utils";
import { SearchSuggestionsResult } from "@/types/search";
import CartButtonProduct from "../cart/CartButtonProduct";

interface SearchSectionProps {
  query?: string;
}

const SearchSection = ({
  query
}: SearchSectionProps) => {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState(query || "")
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [suggestions, setSuggestions] = useState<SearchSuggestionsResult>({ products: [], firstCategory: null })
  const [isLoading, setIsLoading] = useState(false)
  const searchRef = useRef<HTMLDivElement>(null)
  const debounceRef = useRef<NodeJS.Timeout | null>(null)

  // Debounced function to fetch suggestions
  const fetchSuggestions = useCallback(async (searchQuery: string) => {
    if (searchQuery.length < 3) {
      setSuggestions({ products: [], firstCategory: null })
      setShowSuggestions(false)
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch(`/api/search/suggestions?query=${encodeURIComponent(searchQuery)}`)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result: SearchSuggestionsResult = await response.json()
      setSuggestions(result)
      setShowSuggestions(result.products.length > 0 || result.firstCategory !== null)
    } catch (error) {
      console.error('Error fetching suggestions:', error)
      setSuggestions({ products: [], firstCategory: null })
      setShowSuggestions(false)
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Handle search input change
  const handleInputChange = (value: string) => {
    setSearchTerm(value)

    // Clear previous debounce
    if (debounceRef.current) {
      clearTimeout(debounceRef.current)
    }

    // Debounce the suggestions fetch
    debounceRef.current = setTimeout(() => {
      fetchSuggestions(value)
    }, 300)
  }

  // Handle search submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchTerm.trim()) {
      // Close suggestions dropdown
      setShowSuggestions(false)

      const params = new URLSearchParams()
      params.set('query', searchTerm.trim())
      // if (suggestions.firstCategory) {
      //   params.set('category3', suggestions.firstCategory.id)
      // }
      router.push(`/search?${params.toString()}`)
    }
  }

  // Handle Enter key
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      // Close suggestions dropdown
      setShowSuggestions(false)

      if (searchTerm.trim()) {
        const params = new URLSearchParams()
        params.set('query', searchTerm.trim())
        // if (suggestions.firstCategory) {
        //   params.set('category3', suggestions.firstCategory.id)
        // }
        router.push(`/search?${params.toString()}`)
      }
    }
  }

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSuggestions(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Update search term when query prop changes
  useEffect(() => {
    if (query !== undefined) {
      setSearchTerm(query)
      if (query.length >= 3) {
        fetchSuggestions(query)
      } else {
        setSuggestions({ products: [], firstCategory: null })
        setShowSuggestions(false)
      }
    }
  }, [query, fetchSuggestions])

  // Cleanup debounce on unmount
  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current)
      }
    }
  }, [])

  return (
    <div className="w-full max-w-2xl mx-auto p-2 md:p-4" ref={searchRef}>
      <form onSubmit={handleSearch} className="flex flex-wrap md:flex-nowrap gap-2 relative">
        <div className="relative flex-1">
          <Input
            type="text"
            value={searchTerm}
            onChange={(e) => handleInputChange(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Cauta produse dupa nume sau cod OE..."
            className="w-full pl-10 pr-4 h-12 text-base"
          />
          <Search
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            size={20}
          />

          {/* Suggestions Dropdown */}
          {(showSuggestions || isLoading) && (
            <div className="absolute top-full left-0 right-0 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-50 mt-1">
              {/* Loading State */}
              {isLoading && (
                <div className="flex items-center justify-center p-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#0066B1]"></div>
                  <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">Se caută...</span>
                </div>
              )}

              {/* Products Suggestions */}
              {!isLoading && suggestions.products.map((product) => (
                <div
                  key={product.Material_Number}
                  className="flex items-center gap-3 p-3 hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-100 dark:border-gray-600 last:border-b-0"
                >
                  <Link
                    href={`/product/${product.Material_Number}`}
                    className="flex items-center gap-3 flex-1 min-w-0"
                    onClick={() => setShowSuggestions(false)}
                  >
                    <Image
                      src={product.ImageUrl[0] || "https://op47vimj99.ufs.sh/f/6Hnm5nafTbm9yw7hTO5p9dqwM0gSzW5riAs8G7cYPaytnUOu"}
                      alt={product.Description_Local || ""}
                      width={48}
                      height={48}
                      className="w-12 h-12 object-cover rounded"
                    />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                        {product.Description_Local}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {product.Material_Number}
                      </p>
                    </div>
                  </Link>

                  <div className="flex items-center gap-2">
                    <div className="flex flex-col items-end">
                      {product.HasDiscount && product.PretAM && (
                        <span className="text-xs text-gray-400 line-through">
                          {formatPriceRON(product.PretAM)}
                        </span>
                      )}
                      <span className="text-sm font-bold text-red-500">
                        {formatPriceRON(product.FinalPrice)}
                      </span>
                      {product.HasDiscount && product.activeDiscountType && product.activeDiscountValue && (
                        <span className="text-xs text-red-500">
                          {formatDiscount(product.activeDiscountType, product.activeDiscountValue)}
                        </span>
                      )}
                    </div>

                    {/* Cart Button */}
                    <div onClick={(e) => e.stopPropagation()}>
                      { product.FinalPrice ? (
                        <CartButtonProduct product={product.Material_Number} />
                      ) : (
                        <Button
                          disabled 
                          size="icon" 
                          className="p-2 rounded-full bg-[#0066B1] hover:bg-[#004d85] text-white shadow-sm transition-colors"
                        >
                          <X className="w-5 h-5" color="red"/>
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {/* See all results button */}
              {!isLoading && suggestions.firstCategory && (
                <Link
                  href={`/search?query=${encodeURIComponent(searchTerm)}`}
                  className="flex items-center justify-center gap-2 p-3 text-[#0066B1] hover:bg-gray-50 dark:hover:bg-gray-700 font-medium"
                  onClick={() => setShowSuggestions(false)}
                >
                  <Search size={16} />
                    Vezi toate rezultatele pentru &ldquo;{searchTerm}&rdquo;
                </Link>
              )}
            </div>
          )}
        </div>

        <Button
          type="submit"
          className="h-12 px-6 bg-[#0066B1] hover:bg-[#004d85] text-white"
        >
          Cauta
        </Button>
      </form>
    </div>
  );
};

export default SearchSection;

