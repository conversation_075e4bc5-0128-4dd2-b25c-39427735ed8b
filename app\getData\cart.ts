"server-only"

import { logger } from "@/lib/logger";
import { redis } from "@/lib/redis";
import { cuidSchema } from "@/lib/zod";
import { Cart } from "@/types/cart";
import { cache } from 'react'

export const getCart = cache(async (userIdDB: string): Promise<Cart> => {
  console.log("getCart called");
  if (!userIdDB) {
    logger.warn(`[getCart] No userId provided`);
    return { items: [], order: { notes: "" } };
  }

  const userIdParsed = cuidSchema.safeParse(userIdDB);

  if (!userIdParsed.success) {
    logger.error("[getCart] Invalid user ID provided:", userIdParsed.error.format());
    return { items: [], order: { notes: "" } };
  }

  if (!redis) {
    logger.error("[getCart] Redis connection not available");
    return { items: [], order: { notes: "" } };
  }

  const userId = userIdParsed.data

  try {
    const cacheKey = `cart-${userId}`;
    const cart: Cart | null = await redis.get(cacheKey);

    if (!cart) {
      return { items: [], order: { notes: "" } };
    }

    return cart;
  } catch (error) {
    logger.error(`[getCart] Error fetching cart: ${error}`);
    return { items: [], order: { notes: "" } };
  }
})