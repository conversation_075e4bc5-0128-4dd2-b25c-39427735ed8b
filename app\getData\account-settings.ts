"server-only"

import { prisma, withRetry } from "@/lib/db";
import { logger } from "@/lib/logger";
import { auth, clerkClient } from "@clerk/nextjs/server";
import { cache } from "react";

export interface AccountSettingsData {
  id: string;
  email: string;
  emailVerified: Date | null;
  firstName: string;
  lastName: string;
  profileImage: string;
  phoneNumber: string | null;
  phoneVerified: boolean;
  bio: string | null;
  jobTitle: string | null;
  department: string | null;
  salutation: 'Dl' | 'Dna' | null;
  preferredLanguage: string | null;
  timezone: string | null;
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  newsletterOptIn: boolean;
  twoFactorEnabled: boolean;
  lastLoginAt: Date | null;
  loginCount: number;
  passwordEnabled: boolean;
  createdAt: Date;
  updatedAt: Date;
  role: string;
}

export interface UserSecurityInfo {
  loginCount: number;
  lastLoginAt: Date | null;
  passwordEnabled: boolean;
  twoFactorEnabled: boolean;
  loginAttempts: number;
  lockoutUntil: Date | null;
}

export interface UserAuditLogEntry {
  id: string;
  action: string;
  entityType: string;
  entityId: string | null;
  details: string | null;
  ipAddress: string | null;
  userAgent: string | null;
  createdAt: Date;
}

export interface UserDevice {
  id: string;
  status: string;
  lastActiveAt: number;
  createdAt: number;
  isMobile?: boolean;
  ipAddress?: string;
  city?: string;
  country?: string;
  browserVersion?: string ;
  browserName?: string ;
  deviceType?: string ;
}

   
/**
 * Fetches comprehensive account settings data for a user
 * Uses React cache to deduplicate requests within a render cycle
 */
export const getAccountSettingsData = cache(async (userId: string): Promise<AccountSettingsData | null> => {
  try {
    const user = await withRetry(() =>
      prisma.user.findUnique({
        where: {
          id: userId,
          isActive: true,
          isSuspended: false,
          deletedAt: null
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          profileImage: true,
          phoneNumber: true,
          bio: true,
          jobTitle: true,
          department: true,
          salutation: true,
          preferredLanguage: true,
          timezone: true,
          emailNotifications: true,
          pushNotifications: true,
          smsNotifications: true,
          newsletterOptIn: true,
          twoFactorEnabled: true,
          lastLoginAt: true,
          loginCount: true,
          passwordEnabled: true,
          createdAt: true,
          updatedAt: true,
          emailVerified: true,
          phoneVerified: true,
          role: true
        }
      })
    );

    if (!user) {
      logger.warn(`[getAccountSettingsData] User not found or inactive: ${userId}`);
      return null;
    }

    return user as AccountSettingsData;
  } catch (error) {
    logger.error(`[getAccountSettingsData] Error fetching account data for user ${userId}:`, error);
    return null;
  }
});

/**
 * Fetches user security information for security settings tab
 */
export const getUserSecurityInfo = cache(async (userId: string): Promise<UserSecurityInfo | null> => {
  try {
    const user = await withRetry(() =>
      prisma.user.findUnique({
        where: {
          id: userId,
          isActive: true,
          isSuspended: false,
          deletedAt: null
        },
        select: {
          loginCount: true,
          lastLoginAt: true,
          passwordEnabled: true,
          twoFactorEnabled: true,
          loginAttempts: true,
          lockoutUntil: true,
        }
      })
    );

    if (!user) {
      logger.warn(`[getUserSecurityInfo] User not found or inactive: ${userId}`);
      return null;
    }

    return user;
  } catch (error) {
    logger.error(`[getUserSecurityInfo] Error fetching security info for user ${userId}:`, error);
    return null;
  }
});

/**
 * Fetches recent audit log entries for the user (for security tab)
 */
export const getUserAuditLogs = cache(async (userId: string, limit: number = 10): Promise<UserAuditLogEntry[]> => {
  try {
    const auditLogs = await withRetry(() =>
      prisma.userAuditLog.findMany({
        where: { userId },
        select: {
          id: true,
          action: true,
          entityType: true,
          entityId: true,
          details: true,
          ipAddress: true,
          userAgent: true,
          createdAt: true,
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
      })
    );

    return auditLogs;
  } catch (error) {
    logger.error(`[getUserAuditLogs] Error fetching audit logs for user ${userId}:`, error);
    return [];
  }
});

export const getDevicesForUserFromClerk = cache(async () : Promise<UserDevice[]> => {
  const { userId } = await auth();
  if (!userId) return []

  try {
    const client = await clerkClient();
    const sessions = await client.sessions.getSessionList({ userId });

    return sessions.data.filter((session) => session.status === 'active').map(session => ({
      id: session.id,
      status: session.status,
      lastActiveAt: session.lastActiveAt,
      createdAt: session.createdAt,
      isMobile: session.latestActivity?.isMobile,
      ipAddress: session.latestActivity?.ipAddress,
      city: session.latestActivity?.city,
      country: session.latestActivity?.country,
      browserVersion: session.latestActivity?.browserVersion,
      browserName: session.latestActivity?.browserName,
      deviceType: session.latestActivity?.deviceType,
  }));
  } catch (error) {
    logger.error(`[getDevicesForUserFromClerk] Error fetching devices for user ${userId}:`, error);
    return [];
  }
  });
  
