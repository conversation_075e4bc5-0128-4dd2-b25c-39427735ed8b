// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id              String    @id @default(cuid())
  email           String    @unique
  emailVerified   DateTime?
  firstName       String
  lastName        String
  profileImage    String
  userAM          String?   @unique
  phoneNumber     String?
  phoneVerified   Boolean?  @default(false)
  newsletterOptIn Boolean   @default(false)

  // Authentication and identity
  externalId       String? @unique // ID from Clerk/Kinde
  externalProvider String? // "clerk", "kinde", etc.

  // User profile
  salutation        MisterMiss?
  role              Rol         @default(inregistratAB)
  jobTitle          String?
  department        String?
  bio               String?     @db.Text
  preferredLanguage String? // For localization
  timezone          String? // User's timezone

  // Permissions and access control
  permissions  String[] // Array of permission keys
  accessGroups UserGroup[] @relation("UserToGroup")

  // User activity tracking
  lastLoginAt    DateTime?
  loginCount     Int       @default(0)
  lastActivityAt DateTime?

  // Account activation
  isActive       Boolean   @default(true)
  inactiveBy     String? // User ID or system ID that deactivated this user
  inactiveAt     DateTime?
  inactiveReason String?   @db.Text

  // Account status
  isSuspended      Boolean   @default(false)
  suspendedBy      String? // User ID or system ID that suspended this user
  suspendedAt      DateTime? @default(now())
  suspensionReason String?   @db.Text

  // Soft delete
  deletedAt     DateTime?
  deletedBy     String? // User ID or system ID that deleted this user
  deletedReason String?   @db.Text

  // Account security
  passwordEnabled  Boolean   @default(false)
  twoFactorEnabled Boolean   @default(false)
  totpEnabled      Boolean   @default(false)
  mfaEnabledAt     DateTime?
  mfaDisabledAt    DateTime?

  // Login security
  loginAttempts Int       @default(0)
  lockoutUntil  DateTime?

  // Notification preferences
  emailNotifications Boolean @default(true)
  pushNotifications  Boolean @default(false)
  smsNotifications   Boolean @default(false)

  legal_accepted_at DateTime?

  // Relations
  orders            Order[]
  wishlist          Wishlist[]
  shippingAddresses ShippingAddress[]
  billingAddresses  BillingAddress[]
  sessions          UserSession[]
  serviceRequests   ServiceRequest[]
  notifications     UserNotification[]
  auditLogs         UserAuditLog[]

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  createdBy String? // User ID or system ID that created this user
  updatedBy String? // User ID or system ID that last updated this user

  @@index([email]) // Good for fast lookups by email
  @@index([lastName, firstName]) // Useful for alphabetical sorting or searching by name
  @@index([phoneNumber]) // For frequently search/validate by phoneNumber
  @@index([lastLoginAt])
  @@index([isActive, deletedAt])
  @@index([externalId])
  @@index([role])
  @@index([lastActivityAt])
  @@index([isSuspended])
}

model UserSession {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  sessionToken  String    @unique
  expiresAt     DateTime
  ipAddress     String?
  userAgent     String?
  deviceId      String?
  location      String? // Geo location info
  lastActiveAt  DateTime?
  isRevoked     Boolean   @default(false)
  revokedReason String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([sessionToken])
  @@index([userId, expiresAt])
  @@index([isRevoked])
}

model UserGroup {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?  @db.Text
  permissions String[] // Array of permission keys

  users  User[]  @relation("UserToGroup")
  orders Order[] //orders placed by users in this group

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  createdBy String?
  updatedBy String?
}

model UserNotification {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  title   String
  message String           @db.Text
  type    NotificationType
  isRead  Boolean          @default(false)
  readAt  DateTime?
  link    String? // Optional link to navigate to

  createdAt DateTime @default(now())

  @@index([userId, isRead])
  @@index([userId, createdAt])
}

model UserAuditLog {
  id     String  @id @default(cuid())
  userId String?
  user   User?   @relation(fields: [userId], references: [id], onDelete: SetNull)

  action      String // e.g., "user.login", "user.update", "order.create"
  entityType  String // e.g., "user", "order", "product"
  entityId    String? // ID of the affected entity
  details     String? @db.Text //DE STERS aceasta coloana
  detailsJson Json?
  ipAddress   String?
  userAgent   String?
  performedBy String? // User ID or system ID that performed the action

  createdAt DateTime @default(now())

  @@index([userId])
  @@index([action])
  @@index([entityType, entityId])
  @@index([createdAt])
}

enum NotificationType {
  INFO
  SUCCESS
  WARNING
  ERROR
}

model Order {
  id             String  @id @default(cuid())
  orderNumber    String  @unique // Human-readable order number (e.g., ORD-2023-00001)
  amount         Decimal @db.Decimal(15, 2)
  isPaid         Boolean @default(false)
  vin            String?
  invoiceAM      String?
  updatesEnabled Boolean @default(true)
  terms          Boolean @default(true)

  // Current status (denormalized for quick access)
  orderStatus    OrderStatus    @default(plasata)
  paymentStatus  PaymentStatus  @default(asteptare)
  paymentMethod  PaymentMethod  @default(ramburs)
  shippingMethod ShippingMethod @default(curier)
  shipmentStatus ShipmentStatus @default(asteptare)
  showroom       Showroom?

  // Timestamps for key status changes (denormalized for quick access)
  placedAt    DateTime  @default(now())
  processedAt DateTime?
  completedAt DateTime?
  cancelledAt DateTime?

  // Shipping timestamps
  shippingProcessedAt DateTime?
  shippedAt           DateTime?
  deliveredAt         DateTime?

  // Payment timestamps
  paidAt     DateTime?
  refundedAt DateTime?

  // Audit fields
  createdBy String?
  updatedBy String?
  version   Int     @default(1)

  // Soft delete
  isActive   Boolean   @default(true)
  deletedAt  DateTime?
  archivedAt DateTime?

  // Relations
  orderItems    OrderItem[]
  statusHistory OrderStatusHistory[]

  groupId String?
  group   UserGroup? @relation(fields: [groupId], references: [id])

  notes String?

  billingAddressId  String?
  billingAddress    BillingAddress?  @relation(fields: [billingAddressId], references: [id], onDelete: Restrict)
  shippingAddressId String?
  shippingAddress   ShippingAddress? @relation(fields: [shippingAddressId], references: [id], onDelete: Restrict)
  userId            String
  user              User             @relation(fields: [userId], references: [id], onDelete: Restrict)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Add these relations
  returns         Return[]
  serviceRequests ServiceRequest[]

  // Add these fields
  hasReturns         Boolean @default(false)
  hasServiceRequests Boolean @default(false)

  @@index([orderNumber])
  @@index([userId])
  @@index([orderStatus])
  @@index([createdAt])
  @@index([placedAt, orderStatus])
  @@index([userId, orderStatus, createdAt])
  @@index([orderStatus, paymentStatus])
  @@index([isActive, deletedAt])
  @@index([archivedAt])
  @@index([hasReturns])
  @@index([hasServiceRequests])
}

model OrderItem {
  id       String  @id @default(cuid())
  quantity Int // @check("quantity > 0") - Add in migration
  price    Decimal @db.Decimal(10, 2) // @check("price >= 0") - Add in migration

  notes          String?
  notesToInvoice Boolean @default(false)
  vinOrderItem   String?

  // Audit fields
  createdBy String?
  updatedBy String?
  version   Int     @default(1)

  orderId   String
  order     Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  productId String  @default(cuid())
  product   Product @relation(fields: [productId], references: [id], onDelete: Restrict)

  createdAt  DateTime     @default(now())
  updatedAt  DateTime     @updatedAt
  ReturnItem ReturnItem[]

  @@unique([orderId, productId]) // Crucial for preventing duplicate items in an order
  @@index([orderId])
  @@index([productId])
  @@index([orderId, createdAt]) // For order item chronology
  @@index([updatedBy])
  @@index([version])
}

model Wishlist {
  id String @id @default(cuid())

  productCode String
  product     Product @relation(fields: [productCode], references: [Material_Number], onDelete: Cascade)
  userId      String
  user        User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([userId, productCode]) // Ensures a user can only have one of each product in their wishlist
  @@index([userId]) // Useful for fetching all items in a user's wishlist
}

model ShippingAddress {
  id          String  @id @default(cuid())
  fullName    String
  address     String
  city        String
  county      String
  phoneNumber String
  notes       String?
  isDefault   Boolean @default(false)

  orders Order[]
  userId String
  user   User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([phoneNumber])
}

model BillingAddress {
  id          String  @id @default(cuid())
  fullName    String
  companyName String?
  address     String
  city        String
  county      String
  cui         String?
  bank        String?
  iban        String?
  isDefault   Boolean @default(false)

  orders Order[]
  userId String
  user   User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([cui])
  @@index([iban]) // Useful for bank details lookup
}

model Banner {
  id             String  @id @default(cuid())
  title          String
  subtitle       String?
  imageUrl       String
  mobileImageUrl String? // Separate image for mobile devices
  callToAction   String?
  buttonText     String? // Text to display on the CTA button
  description    String?
  url            String?

  // Banner placement and display options
  placement       BannerPlacement @default(HOME)
  position        Int             @default(0) // Order of display within placement
  width           String? // CSS width value (e.g., "100%", "500px")
  height          String? // CSS height value
  backgroundColor String? // Background color in hex or CSS color name
  textColor       String? // Text color in hex or CSS color name
  textAlignment   String? // Text alignment (left, center, right)

  // Targeting options
  targetAudience String? // JSON string for audience targeting rules
  deviceTarget   DeviceTarget @default(ALL)

  // Scheduling
  startDate DateTime  @default(now())
  endDate   DateTime?
  isActive  Boolean   @default(true)

  // Analytics
  impressions    Int    @default(0)
  clicks         Int    @default(0)
  conversionRate Float? // Calculated field

  // Metadata
  createdBy String?
  updatedBy String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([placement, position, isActive])
  @@index([startDate, endDate])
  @@index([deviceTarget])
}

enum BannerPlacement {
  HOME
  CATEGORY
  PRODUCT
  CHECKOUT
  SIDEBAR
  HEADER
  FOOTER
  POPUP
  HERO
  CATEGORY_SECTION_LANDING_PAGE
}

enum DeviceTarget {
  ALL
  DESKTOP
  MOBILE
  TABLET
}

//Categorii
model CategoryLevel1 {
  id       String  @id @default(cuid())
  name     String  @unique // Numele categoriei de nivel 1 (ex: "Repair")
  nameRO   String? // Numele în română (opțional)
  afisat   Boolean @default(false) // Indică dacă categoria este afișată în Mega Menu
  imageUrl String? // URL-ul imaginii pentru categoria de nivel 1

  level2Categories CategoryLevel2[] // Relație către nivelul 2

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())
}

model CategoryLevel2 {
  id       String  @id @default(cuid())
  name     String // Numele categoriei de nivel 2 (ex: "Standard parts")
  nameRO   String? // Numele în română (opțional)
  afisat   Boolean @default(false) // Indică dacă categoria este afișată în Mega Menu
  imageUrl String? // URL-ul imaginii pentru categoria de nivel 2

  level1Id         String
  level1           CategoryLevel1   @relation(fields: [level1Id], references: [id])
  level3Categories CategoryLevel3[] // Relație către nivelul 3

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@unique([name, level1Id]) // O categorie de nivel 2 este unică în cadrul părintelui său de nivel 1
}

model CategoryLevel3 {
  id         String  @id @default(cuid())
  name       String // Numele categoriei de nivel 3 (ex: "Bolts")
  nameRO     String? // Numele în română (opțional)
  descriere  String? // Descrierea scurta acategoriei
  afisat     Boolean @default(false) // Indică dacă categoria este afișată în Mega Menu
  familyCode String? @unique // Cheia din `categs.csv` și `allParts.csv`
  imageUrl   String? // URL-ul imaginii pentru categoria de nivel 3

  // SEO and display fields
  slug            String? @unique // URL-friendly name for SEO : https://yourdomain.com/categories/{brake-pads}
  metaTitle       String? // Title tag for SEO: BMW Engine Parts | YourSite, MINI Brake Pads | YourSite
  metaDescription String? // Meta description for SEO: Shop our selection of high-quality BMW engine parts with fast delivery and warranty.
  displayOrder    Int     @default(0)

  // Category statistics (denormalized for performance)
  productCount     Int       @default(0)
  lastProductAdded DateTime?

  // Audit fields
  isActive  Boolean   @default(true) //controle whether the category is active in the system as awhole
  deletedAt DateTime?

  level2Id String
  level2   CategoryLevel2 @relation(fields: [level2Id], references: [id])
  products Product[] // Relație către produse

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@index([familyCode]) // Index pentru căutări rapide după familyCode
  @@index([slug])
  @@index([displayOrder])
  @@index([productCount])
  @@index([isActive, deletedAt])
}

// --- Branduri și Modele ---
model Brand {
  id       String  @id @default(cuid())
  name     String  @unique // Ex: "BMW", "MINI", "MOTO"
  nameRO   String? // Numele în română (opțional)
  afisat   Boolean @default(true) // Indică dacă brandul este afișat în Mega Menu
  imageUrl String? // URL-ul imaginii pentru brand

  productClasses ProductClass[] // Relație către clasele de produse asociate

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())
}

model VehicleModel {
  id   String @id @default(cuid())
  name String @unique // Ex: F34", "R55", "K70"

  productClasses ProductClassVehicleModel[] // Relație many-to-many prin tabela de legătură

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())
}

// --- Clase de Produse (legătura între Parts Class, Brand și Modele) ---

model ProductClass {
  id        String @id @default(cuid())
  classCode String @unique // Cheia din `class.csv` și `allParts.csv` (Parts Class)

  brandId String
  brand   Brand  @relation(fields: [brandId], references: [id])

  vehicleModels ProductClassVehicleModel[] // Relație many-to-many prin tabela de legătură
  products      Product[] // Relație către produsele din această clasă

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@index([classCode]) // Index pentru căutări rapide după classCode
}

// Tabela de legătură Many-to-Many între ProductClass și VehicleModel
model ProductClassVehicleModel {
  productClassId String
  vehicleModelId String

  productClass ProductClass @relation(fields: [productClassId], references: [id])
  vehicleModel VehicleModel @relation(fields: [vehicleModelId], references: [id])

  createdAt DateTime @default(now())

  @@id([productClassId, vehicleModelId]) // Cheie primară compusă
}

model Product {
  id              String @id @default(cuid())
  Material_Number String @unique // Material din `allParts.csv`

  Net_Weight          String?
  Description_Local   String?
  Base_Unit_Of_Measur String?
  Cross_Plant         String?
  New_Material        String?

  PretAM              Decimal?      @db.Decimal(15, 2) // @check("PretAM >= 0") - Add in migration
  FinalPrice          Decimal?      @db.Decimal(15, 2) // @check("FinalPrice >= 0") - Add in migration
  HasDiscount         Boolean       @default(false)
  activeDiscountType  DiscountType? // The type of discount currently applied   PERCENTAGE   // DiscountValue is a percentage (e.g., 10 for 10%)   FIXED_AMOUNT // DiscountValue is a fixed amount to subtract (e.g., 5 for $5 off)   NEW_PRICE    // DiscountValue is the new absolute price for the product
  activeDiscountValue Decimal?      @db.Decimal(10, 2) // The actual discount value (percentage, amount, or new price)
  discountPercentage  Decimal?      @db.Decimal(5, 2) // Calculated percentage off for efficient filtering
  priceRange          String? // e.g., "0-50", "50-100", "100-200", "200+"

  ImageUrl        String[] @default(["https://op47vimj99.ufs.sh/f/6Hnm5nafTbm964jRPnfTbm9EeHnDOzysS6K5X27Upql8xtjN"]) // URL-ul imaginii pentru produs
  IsOnLandingPage Boolean  @default(false)

  // Search optimization fields
  Material_Number_normalized   String? // Lowercase, trimmed version for search
  Description_Local_normalized String? // For case-insensitive search

  // Stock and availability
  stockStatus StockStatus @default(UNKNOWN)

  // Audit and versioning
  createdBy String?
  updatedBy String?
  version   Int     @default(1)

  // Soft delete
  isActive  Boolean   @default(true)
  deletedAt DateTime?

  attributes              ProductAttribute[]
  productHistory          ProductHistory[] // Relație către istoricul produsului
  productAttributeHistory ProductAttributeHistory[] // Relație către istoricul produsului
  discounts               ProductDiscount[]
  orderItems              OrderItem[]
  wishlist                Wishlist[]
  PriceHistory            PriceHistory[]

  Parts_Class  String?       @default("undefined-class")
  classId      String? // This will store the 'id' of the ProductClass
  productClass ProductClass? @relation(fields: [classId], references: [id], onDelete: SetNull)

  Material_Group   String?         @default("undefined-category")
  categoryLevel3Id String? // This will store the 'id' of the CategoryLevel3
  categoryLevel3   CategoryLevel3? @relation(fields: [categoryLevel3Id], references: [id], onDelete: SetNull)

  // Add these relations
  serviceItems ServiceItem[]

  // Add these fields
  isServiceable  Boolean @default(false)
  warrantyMonths Int?

  createdAt       DateTime @default(now())
  last_updated_at DateTime @default(now())

  @@index([HasDiscount, FinalPrice]) // For filtering discounted products and sorting by price
  @@index([categoryLevel3Id, HasDiscount]) // For category + discount filtering
  @@index([categoryLevel3Id, FinalPrice]) // For category + price sorting
  @@index([priceRange, HasDiscount]) // For price range filtering with discounts
  @@index([discountPercentage]) // For sorting by discount percentage
  @@index([Material_Number]) // Index pentru căutări/update-uri rapide după SKU
  @@index([Material_Group])
  @@index([Parts_Class])
  @@index([Material_Group, Parts_Class]) // For filtering by category and class
  @@index([categoryLevel3Id, Material_Group]) // For category-based queries
  @@index([PretAM, HasDiscount]) // For price-based filtering
  @@index([IsOnLandingPage, categoryLevel3Id]) // For landing page products by category
  @@index([createdAt, categoryLevel3Id]) // For time-based category queries
  @@index([Material_Number_normalized])
  @@index([Description_Local_normalized])
  @@index([stockStatus])
  @@index([isActive, deletedAt])
  @@index([updatedBy])
  @@index([version])
}

model PriceHistory {
  id        String  @id @default(cuid())
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  oldPretAM     Decimal? @db.Decimal(15, 2)
  newPretAM     Decimal? @db.Decimal(15, 2)
  oldFinalPrice Decimal? @db.Decimal(15, 2)
  newFinalPrice Decimal? @db.Decimal(15, 2)

  reason String? // e.g., "Weekly update", "Discount applied", etc.
  source String? // e.g., "CSV import", "Manual adjustment", etc.

  createdBy String?
  createdAt DateTime @default(now())

  @@index([productId])
  @@index([productId, createdAt])
  @@index([createdAt])
}

model ProductHistory {
  id              String     @id @default(uuid()) @db.Uuid
  Material_Number String
  changes         Json
  snapshot        Json
  change_type     ChangeType
  version         Int
  changed_by      String

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  product Product @relation(fields: [Material_Number], references: [Material_Number], onDelete: Cascade)
}

model Discount {
  id          String       @id @default(cuid())
  name        String       @unique
  description String
  type        DiscountType
  value       Decimal      @db.Decimal(10, 2)
  startDate   DateTime
  endDate     DateTime
  active      Boolean      @default(false)
  createdBy   String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  productDiscounts ProductDiscount[]
  discountHistory  DiscountHistory[]

  @@index([active])
  @@index([name])
}

model ProductDiscount {
  id String @id @default(cuid())

  productId  String   @unique // This ensures one product can only have one discount
  product    Product  @relation(fields: [productId], references: [id])
  discountId String
  discount   Discount @relation(fields: [discountId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([discountId]) // Keep an index on discountId for queries
}

model DiscountHistory {
  id          String     @id @default(cuid())
  discountId  String
  changes     Json
  snapshot    Json
  change_type ChangeType
  version     Int
  changed_by  String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  discount Discount @relation(fields: [discountId], references: [id])
}

model ProductAttribute {
  id              String  @id @default(cuid())
  Material_Number String
  key             String?
  value           String?

  product Product @relation(fields: [Material_Number], references: [Material_Number])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([Material_Number, key])
  @@index([key, value])
  @@index([Material_Number])
}

model ProductAttributeHistory {
  id              String     @id @default(cuid())
  Material_Number String
  changes         Json
  snapshot        Json
  change_type     ChangeType
  version         Int
  changed_by      String

  product Product @relation(fields: [Material_Number], references: [Material_Number], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([Material_Number])
}

enum ChangeType {
  CRON_DISCOUNT_EXPIRED
  MANUAL_DISCOUNT_EXPIRED_BUTTON_PRESS
  INSERT
  UPDATE
  DELETE
  ORDER
  DISCOUNT
  PRODUCT_ADDED
  PRODUCT_DELETE_ALL
  PRODUCT_DELETE
  PRODUCT_ADD_TO_DISCOUNT_WITH_CSV
  ADDED_TO_DISCOUNT
  DELETED_FROM_DISCOUNT
}

enum DiscountType {
  PERCENTAGE // DiscountValue is a percentage (e.g., 10 for 10%)
  FIXED_AMOUNT // DiscountValue is a fixed amount to subtract (e.g., 5 for $5 off)1
  NEW_PRICE // DiscountValue is the new absolute price for the product
}

enum MisterMiss {
  Dl
  Dna
}

enum Rol {
  administAB
  moderatorAB
  inregistratAB
  fourLvlAdminAB
  fourLvlInregistratAB
  angajatAB
}

enum Showroom {
  CJ
  BV
  TM
  AR
  BAC
  BAN
  OTP
  MIL
  TGM
  JIL
  CT
  CRA
  SB
}

enum OrderStatus {
  plasata // Placed
  procesare // Processing
  confirmata // Confirmed
  pregatita // Prepared
  expediata // Shipped
  livrata // Delivered
  completa // Complete
  anulata // Cancelled
  stornata // Voided
  returnata // Returned
  partiala // Partially fulfilled
}

enum ShippingMethod {
  curier
  showroom
}

enum ShipmentStatus {
  asteptare // Waiting
  prelucrare // Processing
  pregatit // Ready
  expediat // Shipped
  tranzit // In transit
  livrat // Delivered
  esuat // Failed
  intors // Returned
  anulat // Cancelled
  partial // Partially shipped
}

enum PaymentMethod {
  ramburs
  card
  transfer
  laTermen
}

enum PaymentStatus {
  asteptare
  succes
  esuat
  rambursat
  partial_rambursat
  contestat
}

enum StockStatus {
  IN_STOCK
  LOW_STOCK
  OUT_OF_STOCK
  DISCONTINUED
  UNKNOWN
}

model OrderStatusHistory {
  id      String @id @default(cuid())
  orderId String
  order   Order  @relation(fields: [orderId], references: [id], onDelete: Cascade)

  // Status changes
  orderStatus    OrderStatus?
  paymentStatus  PaymentStatus?
  shipmentStatus ShipmentStatus?

  // Previous values
  previousOrderStatus    OrderStatus?
  previousPaymentStatus  PaymentStatus?
  previousShipmentStatus ShipmentStatus?

  // Metadata
  reason    String?
  notes     String?
  changedBy String?
  ipAddress String?
  userAgent String?

  createdAt DateTime @default(now())

  @@index([orderId])
  @@index([orderId, createdAt])
  @@index([orderStatus])
  @@index([shipmentStatus])
  @@index([paymentStatus])
  @@index([changedBy])
}

model Return {
  id           String @id @default(cuid())
  returnNumber String @unique // Human-readable return number (e.g., RET-2023-00001)

  // Related order
  orderId String
  order   Order  @relation(fields: [orderId], references: [id], onDelete: Restrict)

  // Status tracking
  status          ReturnStatus @default(requested)
  reason          ReturnReason
  additionalNotes String?      @db.Text

  // Approval details
  isApproved      Boolean? // null = pending, true/false = decision made
  approvedBy      String?
  approvedAt      DateTime?
  rejectionReason String?   @db.Text

  // Financial details
  refundAmount    Decimal?      @db.Decimal(15, 2)
  refundMethod    RefundMethod?
  refundedAt      DateTime?
  refundReference String?

  // Logistics
  returnShippingLabel String? // URL or reference to shipping label
  receivedAt          DateTime? // When items were received back
  inspectedAt         DateTime? // When items were inspected

  // Items being returned
  returnItems   ReturnItem[]
  statusHistory ReturnStatusHistory[]

  // Audit fields
  createdBy String
  updatedBy String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([orderId])
  @@index([status])
  @@index([createdAt])
  @@index([returnNumber])
}

model ReturnItem {
  id String @id @default(cuid())

  // Related return and original order item
  returnId    String
  return      Return    @relation(fields: [returnId], references: [id], onDelete: Cascade)
  orderItemId String
  orderItem   OrderItem @relation(fields: [orderItemId], references: [id], onDelete: Restrict)

  // Return details
  quantity    Int
  reason      ReturnItemReason
  condition   ItemCondition    @default(asDescribed)
  description String?          @db.Text

  // Processing details
  isReceived       Boolean           @default(false)
  isInspected      Boolean           @default(false)
  inspectionNotes  String?           @db.Text
  inspectionResult InspectionResult?

  // Audit fields
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([returnId, orderItemId]) // Prevent duplicate items in a return
  @@index([returnId])
  @@index([orderItemId])
}

model ReturnStatusHistory {
  id       String @id @default(cuid())
  returnId String
  return   Return @relation(fields: [returnId], references: [id], onDelete: Cascade)

  previousStatus ReturnStatus?
  newStatus      ReturnStatus

  notes     String? @db.Text
  changedBy String

  createdAt DateTime @default(now())

  @@index([returnId])
  @@index([returnId, createdAt])
}

model ServiceRequest {
  id            String @id @default(cuid())
  serviceNumber String @unique // Human-readable service number (e.g., SRV-2023-00001)

  // Customer and vehicle info
  userId       String
  user         User    @relation(fields: [userId], references: [id], onDelete: Restrict)
  vin          String? // Vehicle identification number
  vehicleModel String?
  vehicleYear  Int?
  mileage      Int?

  // Service details
  type           ServiceType   @default(repair)
  status         ServiceStatus @default(requested)
  description    String        @db.Text
  diagnosisNotes String?       @db.Text

  // Scheduling
  preferredDate     DateTime?
  scheduledDate     DateTime?
  completedDate     DateTime?
  estimatedDuration Int? // In minutes

  // Financial
  estimatedCost Decimal?  @db.Decimal(15, 2)
  finalCost     Decimal?  @db.Decimal(15, 2)
  isPaid        Boolean   @default(false)
  paidAt        DateTime?

  // Related items
  serviceItems  ServiceItem[]
  statusHistory ServiceStatusHistory[]

  // Audit fields
  createdBy String
  updatedBy String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  Order     Order[]

  @@index([userId])
  @@index([status])
  @@index([serviceNumber])
  @@index([scheduledDate])
  @@index([vin])
}

model ServiceItem {
  id String @id @default(cuid())

  serviceRequestId String
  serviceRequest   ServiceRequest @relation(fields: [serviceRequestId], references: [id], onDelete: Cascade)

  // Can be linked to a product or just described
  productId String?
  product   Product? @relation(fields: [productId], references: [id], onDelete: SetNull)

  description String
  quantity    Int      @default(1)
  unitPrice   Decimal? @db.Decimal(15, 2)
  totalPrice  Decimal? @db.Decimal(15, 2)

  // Labor vs Parts
  itemType   ServiceItemType @default(part)
  laborHours Decimal?        @db.Decimal(5, 2)

  // Audit fields
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([serviceRequestId])
  @@index([productId])
}

model ServiceStatusHistory {
  id               String         @id @default(cuid())
  serviceRequestId String
  serviceRequest   ServiceRequest @relation(fields: [serviceRequestId], references: [id], onDelete: Cascade)

  previousStatus ServiceStatus?
  newStatus      ServiceStatus

  notes     String? @db.Text
  changedBy String

  createdAt DateTime @default(now())

  @@index([serviceRequestId])
  @@index([serviceRequestId, createdAt])
}

model CategorySection {
  id          String @id @default(cuid())
  name        String
  description String
  image       String
  href        String
}

// Return-related enums
enum ReturnStatus {
  requested
  approved
  rejected
  awaitingReceipt
  received
  inspected
  refundIssued
  completed
  cancelled
}

enum ReturnReason {
  wrongItem
  defective
  damaged
  notAsDescribed
  noLongerWanted
  other
}

enum ReturnItemReason {
  wrongItem
  defective
  damaged
  notAsDescribed
  noLongerWanted
  other
}

enum ItemCondition {
  asDescribed
  damaged
  opened
  used
  missingParts
}

enum InspectionResult {
  approved
  rejected
  partiallyApproved
}

enum RefundMethod {
  originalPayment
  storeCredit
  bankTransfer
}

// Service-related enums
enum ServiceType {
  repair
  maintenance
  warranty
  installation
  inspection
  other
}

enum ServiceStatus {
  requested
  scheduled
  inProgress
  diagnosisComplete
  awaitingParts
  awaitingApproval
  completed
  cancelled
  delivered
}

enum ServiceItemType {
  part
  labor
  fee
  other
}

//triggers that i use:

/**
 * CREATE OR REPLACE FUNCTION log_product_history()
 * RETURNS TRIGGER AS $$
 * DECLARE
 * new_version INT;
 * changes_json jsonb := '{}'::jsonb;
 * field_name TEXT;
 * old_value jsonb;
 * new_value jsonb;
 * BEGIN
 * -- Determine the version number
 * SELECT COALESCE(MAX(version), 0) + 1 INTO new_version
 * FROM "ProductHistory"
 * WHERE "Material_Number" = NEW."Material_Number";
 * -- Handle INSERT operation
 * IF (TG_OP = 'INSERT') THEN
 * INSERT INTO "ProductHistory" (
 * "id", "Material_Number", "changes", "updatedAt", "snapshot", "change_type", "version", "changed_by"
 * )
 * VALUES
 * (
 * gen_random_uuid(),
 * NEW."Material_Number",
 * '{}'::jsonb,   -- No changes in the insert case
 * NOW(),
 * to_jsonb(NEW),
 * 'INSERT',
 * new_version,
 * 'T-GIS'
 * );
 * -- Handle UPDATE operation
 * ELSIF (TG_OP = 'UPDATE') THEN
 * -- Iterate through each field to detect changes
 * FOR field_name, old_value IN
 * SELECT key, value
 * FROM jsonb_each(to_jsonb(OLD))
 * LOOP
 * -- *** Add this condition to skip the specific field ***
 * IF field_name = 'last_updated_at' THEN
 * CONTINUE; -- Skips the rest of the loop body for this iteration
 * END IF;
 * SELECT value INTO new_value
 * FROM jsonb_each(to_jsonb(NEW))
 * WHERE key = field_name;
 * -- Check if the value has changed
 * IF old_value IS DISTINCT FROM new_value THEN
 * changes_json := jsonb_set(changes_json, ARRAY[field_name], new_value);
 * END IF;
 * END LOOP;
 * -- Only insert a record if there are actual changes
 * IF changes_json <> '{}'::jsonb THEN
 * INSERT INTO "ProductHistory" (
 * "id", "Material_Number", "changes", "updatedAt", "snapshot", "change_type", "version", "changed_by"
 * )
 * VALUES
 * (
 * gen_random_uuid(),
 * NEW."Material_Number",
 * changes_json,
 * NOW(),
 * to_jsonb(NEW),
 * 'UPDATE',
 * new_version,
 * 'T-GIS'
 * );
 * END IF;
 * END IF;
 * RETURN NEW;
 * END;
 * $$ LANGUAGE plpgsql;
 * DROP TRIGGER IF EXISTS product_history_trigger ON "Product";
 * CREATE TRIGGER product_history_trigger
 * AFTER INSERT OR UPDATE ON "Product"
 * FOR EACH ROW
 * EXECUTE FUNCTION log_product_history();
 * --------------------------------------------
 * -- Create a trigger function to log price changes
 * CREATE OR REPLACE FUNCTION log_price_history()
 * RETURNS TRIGGER AS $$
 * BEGIN
 * -- Only create a history record if price actually changed
 * IF (OLD."PretAM" IS DISTINCT FROM NEW."PretAM" OR
 * OLD."FinalPrice" IS DISTINCT FROM NEW."FinalPrice") THEN
 * INSERT INTO "PriceHistory" (
 * "id",
 * "productId",
 * "oldPretAM",
 * "newPretAM",
 * "oldFinalPrice",
 * "newFinalPrice",
 * "reason",
 * "source",
 * "createdBy",
 * "createdAt"
 * )
 * VALUES (
 * gen_random_uuid(),
 * NEW."id",
 * OLD."PretAM",
 * NEW."PretAM",
 * OLD."FinalPrice",
 * NEW."FinalPrice",
 * CASE
 * WHEN OLD."PretAM" IS DISTINCT FROM NEW."PretAM" THEN 'Base price update'
 * ELSE 'Discount change'
 * END,
 * 'System trigger',
 * 'system',
 * NOW()
 * );
 * END IF;
 * RETURN NEW;
 * END;
 * $$ LANGUAGE plpgsql;
 * -- Create the trigger
 * DROP TRIGGER IF EXISTS product_price_history_trigger ON "Product";
 * CREATE TRIGGER product_price_history_trigger
 * AFTER UPDATE OF "PretAM", "FinalPrice" ON "Product"
 * FOR EACH ROW
 * EXECUTE FUNCTION log_price_history();
 * --------------------------------------------------------------
 * -- Create a function to calculate final price based on discount
 * CREATE OR REPLACE FUNCTION calculate_final_price()
 * RETURNS TRIGGER AS $$
 * DECLARE
 * v_discount_type TEXT;
 * v_discount_value DECIMAL;
 * v_has_discount BOOLEAN := FALSE;
 * v_discount_percentage DECIMAL := NULL;
 * BEGIN
 * -- Check if product has a discount
 * SELECT d."type", d."value" INTO v_discount_type, v_discount_value
 * FROM "ProductDiscount" pd
 * JOIN "Discount" d ON pd."discountId" = d."id"
 * WHERE pd."productId" = NEW."id" AND d."active" = TRUE
 * AND d."startDate" <= CURRENT_TIMESTAMP
 * AND d."endDate" >= CURRENT_TIMESTAMP
 * LIMIT 1;
 * -- If no active discount found, final price equals base price
 * IF v_discount_type IS NULL THEN
 * NEW."FinalPrice" := NEW."PretAM";
 * NEW."HasDiscount" := FALSE;
 * NEW."discountPercentage" := NULL;
 * ELSE
 * -- Calculate final price based on discount type
 * v_has_discount := TRUE;
 * CASE v_discount_type
 * WHEN 'PERCENTAGE' THEN
 * -- Ensure percentage is within valid range (0-100)
 * IF v_discount_value > 0 AND v_discount_value <= 100 THEN
 * NEW."FinalPrice" := NEW."PretAM" - (NEW."PretAM" * v_discount_value / 100);
 * v_discount_percentage := v_discount_value;
 * ELSE
 * NEW."FinalPrice" := NEW."PretAM";
 * v_has_discount := FALSE;
 * END IF;
 * WHEN 'FIXED_AMOUNT' THEN
 * -- Ensure fixed amount doesn't make price negative
 * IF v_discount_value < NEW."PretAM" THEN
 * NEW."FinalPrice" := NEW."PretAM" - v_discount_value;
 * -- Calculate equivalent percentage for display
 * v_discount_percentage := (v_discount_value / NEW."PretAM") * 100;
 * ELSE
 * NEW."FinalPrice" := 0;
 * v_discount_percentage := 100;
 * END IF;
 * WHEN 'NEW_PRICE' THEN
 * -- New price becomes the final price
 * IF v_discount_value < NEW."PretAM" THEN
 * NEW."FinalPrice" := v_discount_value;
 * -- Calculate equivalent percentage for display
 * v_discount_percentage := ((NEW."PretAM" - v_discount_value) / NEW."PretAM") * 100;
 * ELSE
 * -- If new price is higher than base price, don't apply discount
 * NEW."FinalPrice" := NEW."PretAM";
 * v_has_discount := FALSE;
 * END IF;
 * ELSE
 * -- Unknown discount type, use base price
 * NEW."FinalPrice" := NEW."PretAM";
 * v_has_discount := FALSE;
 * END CASE;
 * NEW."HasDiscount" := v_has_discount;
 * NEW."discountPercentage" := v_discount_percentage;
 * END IF;
 * RETURN NEW;
 * END;
 * $$ LANGUAGE plpgsql;
 * -- Create trigger to recalculate final price when base price changes
 * DROP TRIGGER IF EXISTS update_product_price ON "Product";
 * CREATE TRIGGER update_product_price
 * BEFORE INSERT OR UPDATE OF "PretAM" ON "Product"
 * FOR EACH ROW
 * EXECUTE FUNCTION calculate_final_price();
 * -- Create trigger to recalculate prices when discounts are added/removed
 * CREATE OR REPLACE FUNCTION update_product_price_on_discount_change()
 * RETURNS TRIGGER AS $$
 * BEGIN
 * -- Update the product to trigger the calculate_final_price function
 * IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
 * UPDATE "Product" SET "id" = "id" WHERE "id" = NEW."productId";
 * ELSIF TG_OP = 'DELETE' THEN
 * UPDATE "Product" SET "id" = "id" WHERE "id" = OLD."productId";
 * END IF;
 * RETURN NULL;
 * END;
 * $$ LANGUAGE plpgsql;
 * -- Create triggers for discount changes
 * DROP TRIGGER IF EXISTS product_discount_insert_trigger ON "ProductDiscount";
 * DROP TRIGGER IF EXISTS product_discount_update_trigger ON "ProductDiscount";
 * DROP TRIGGER IF EXISTS product_discount_delete_trigger ON "ProductDiscount";
 * CREATE TRIGGER product_discount_insert_trigger
 * AFTER INSERT ON "ProductDiscount"
 * FOR EACH ROW
 * EXECUTE FUNCTION update_product_price_on_discount_change();
 * CREATE TRIGGER product_discount_update_trigger
 * AFTER UPDATE ON "ProductDiscount"
 * FOR EACH ROW
 * EXECUTE FUNCTION update_product_price_on_discount_change();
 * CREATE TRIGGER product_discount_delete_trigger
 * AFTER DELETE ON "ProductDiscount"
 * FOR EACH ROW
 * EXECUTE FUNCTION update_product_price_on_discount_change();
 * -- Also update prices when discount details change
 * CREATE OR REPLACE FUNCTION update_products_on_discount_change()
 * RETURNS TRIGGER AS $$
 * BEGIN
 * -- Update all products using this discount to recalculate prices
 * IF TG_OP = 'UPDATE' AND (
 * OLD."value" IS DISTINCT FROM NEW."value" OR
 * OLD."type" IS DISTINCT FROM NEW."type" OR
 * OLD."active" IS DISTINCT FROM NEW."active" OR
 * OLD."startDate" IS DISTINCT FROM NEW."startDate" OR
 * OLD."endDate" IS DISTINCT FROM NEW."endDate"
 * ) THEN
 * UPDATE "Product" p
 * SET "id" = p."id"
 * FROM "ProductDiscount" pd
 * WHERE pd."productId" = p."id" AND pd."discountId" = NEW."id";
 * END IF;
 * RETURN NULL;
 * END;
 * $$ LANGUAGE plpgsql;
 * CREATE TRIGGER discount_update_trigger
 * AFTER UPDATE ON "Discount"
 * FOR EACH ROW
 * EXECUTE FUNCTION update_products_on_discount_change();
 * ------------------------------------------------------
 * for Bulk Discount Support
 * -- Modify the calculate_final_price function to check for bulk discounts - a familiyCode
 * CREATE OR REPLACE FUNCTION calculate_final_price()
 * RETURNS TRIGGER AS $$
 * DECLARE
 * v_discount_type TEXT;
 * v_discount_value DECIMAL;
 * v_has_discount BOOLEAN := FALSE;
 * v_discount_percentage DECIMAL := NULL;
 * v_discount_record RECORD;
 * BEGIN
 * -- First check for direct product discount
 * SELECT d."type", d."value" INTO v_discount_type, v_discount_value
 * FROM "ProductDiscount" pd
 * JOIN "Discount" d ON pd."discountId" = d."id"
 * WHERE pd."productId" = NEW."id" AND d."active" = TRUE
 * AND d."startDate" <= CURRENT_TIMESTAMP
 * AND d."endDate" >= CURRENT_TIMESTAMP
 * LIMIT 1;
 * -- If no direct discount, check for category/brand/class discounts
 * IF v_discount_type IS NULL THEN
 * SELECT d.* INTO v_discount_record
 * FROM "Discount" d
 * WHERE d."active" = TRUE
 * AND d."startDate" <= CURRENT_TIMESTAMP
 * AND d."endDate" >= CURRENT_TIMESTAMP
 * AND (
 * (d."applyToCategory" IS NOT NULL AND d."applyToCategory" = NEW."Material_Group") OR
 * (d."applyToBrand" IS NOT NULL AND EXISTS (
 * SELECT 1 FROM "ProductClass" pc
 * WHERE pc."id" = NEW."classId"
 * AND pc."brandId" = d."applyToBrand"
 * )) OR
 * (d."applyToClass" IS NOT NULL AND d."applyToClass" = NEW."Parts_Class")
 * )
 * ORDER BY d."priority" DESC
 * LIMIT 1;
 * IF v_discount_record IS NOT NULL THEN
 * v_discount_type := v_discount_record."type";
 * v_discount_value := v_discount_record."value";
 * END IF;
 * END IF;
 * -- Rest of the function remains the same...
 * -- (calculation logic based on discount type)
 * RETURN NEW;
 * END;
 * $$ LANGUAGE plpgsql;
 * ***However, for 400K products, this approach might cause performance issues when updating discounts.
 * ****A more efficient approach would be to pre-calculate and materialize the discounts during off-peak hours.
 */

/**
 * -- Drop existing function to replace it
 * DROP FUNCTION IF EXISTS calculate_final_price();
 * -- Create improved function to calculate final price based on discount
 * CREATE OR REPLACE FUNCTION calculate_final_price()
 * RETURNS TRIGGER AS $$
 * DECLARE
 * v_discount_type TEXT;
 * v_discount_value DECIMAL;
 * v_has_discount BOOLEAN := FALSE;
 * v_discount_percentage DECIMAL := NULL;
 * v_base_price DECIMAL;
 * BEGIN
 * -- Ensure we have a base price to work with
 * IF NEW."PretAM" IS NULL THEN
 * RETURN NEW;
 * END IF;
 * v_base_price := NEW."PretAM";
 * -- Check if product has a discount
 * SELECT d."type", d."value" INTO v_discount_type, v_discount_value
 * FROM "ProductDiscount" pd
 * JOIN "Discount" d ON pd."discountId" = d."id"
 * WHERE pd."productId" = NEW."id" AND d."active" = TRUE
 * AND d."startDate" <= CURRENT_TIMESTAMP
 * AND d."endDate" >= CURRENT_TIMESTAMP
 * LIMIT 1;
 * -- Debug logging (remove in production)
 * RAISE NOTICE 'Product ID: %, Discount Type: %, Discount Value: %',
 * NEW."id", v_discount_type, v_discount_value;
 * -- If no active discount found, final price equals base price
 * IF v_discount_type IS NULL THEN
 * NEW."FinalPrice" := v_base_price;
 * NEW."HasDiscount" := FALSE;
 * NEW."discountPercentage" := NULL;
 * RAISE NOTICE 'No discount found, setting FinalPrice to %', v_base_price;
 * ELSE
 * -- Calculate final price based on discount type
 * v_has_discount := TRUE;
 * CASE v_discount_type
 * WHEN 'PERCENTAGE' THEN
 * -- Ensure percentage is within valid range (0-100)
 * IF v_discount_value > 0 AND v_discount_value <= 100 THEN
 * NEW."FinalPrice" := v_base_price - (v_base_price * v_discount_value / 100);
 * v_discount_percentage := v_discount_value;
 * RAISE NOTICE 'PERCENTAGE discount: % -> %', v_discount_value, NEW."FinalPrice";
 * ELSE
 * NEW."FinalPrice" := v_base_price;
 * v_has_discount := FALSE;
 * RAISE NOTICE 'Invalid percentage: %', v_discount_value;
 * END IF;
 * WHEN 'FIXED_AMOUNT' THEN
 * -- Apply fixed amount discount
 * NEW."FinalPrice" := GREATEST(v_base_price - v_discount_value, 0);
 * -- Calculate equivalent percentage for display
 * IF v_base_price > 0 THEN
 * v_discount_percentage := LEAST((v_discount_value / v_base_price) * 100, 100);
 * ELSE
 * v_discount_percentage := 0;
 * END IF;
 * RAISE NOTICE 'FIXED_AMOUNT discount: % -> %', v_discount_value, NEW."FinalPrice";
 * WHEN 'NEW_PRICE' THEN
 * -- New price becomes the final price if it's lower than base price
 * IF v_discount_value < v_base_price THEN
 * NEW."FinalPrice" := v_discount_value;
 * -- Calculate equivalent percentage for display
 * IF v_base_price > 0 THEN
 * v_discount_percentage := ((v_base_price - v_discount_value) / v_base_price) * 100;
 * ELSE
 * v_discount_percentage := 0;
 * END IF;
 * RAISE NOTICE 'NEW_PRICE discount: % -> %', v_discount_value, NEW."FinalPrice";
 * ELSE
 * -- If new price is higher than base price, don't apply discount
 * NEW."FinalPrice" := v_base_price;
 * v_has_discount := FALSE;
 * RAISE NOTICE 'NEW_PRICE higher than base price: % > %', v_discount_value, v_base_price;
 * END IF;
 * ELSE
 * -- Unknown discount type, use base price
 * NEW."FinalPrice" := v_base_price;
 * v_has_discount := FALSE;
 * RAISE NOTICE 'Unknown discount type: %', v_discount_type;
 * END CASE;
 * NEW."HasDiscount" := v_has_discount;
 * NEW."discountPercentage" := v_discount_percentage;
 * END IF;
 * -- Ensure FinalPrice is never NULL
 * IF NEW."FinalPrice" IS NULL THEN
 * NEW."FinalPrice" := v_base_price;
 * RAISE NOTICE 'FinalPrice was NULL, setting to base price: %', v_base_price;
 * END IF;
 * RETURN NEW;
 * END;
 * $$ LANGUAGE plpgsql;
 * -- Recreate the trigger
 * DROP TRIGGER IF EXISTS update_product_price ON "Product";
 * CREATE TRIGGER update_product_price
 * BEFORE INSERT OR UPDATE ON "Product"
 * FOR EACH ROW
 * EXECUTE FUNCTION calculate_final_price();
 * -- Also ensure the discount change trigger works for all discount types
 * CREATE OR REPLACE FUNCTION update_product_price_on_discount_change()
 * RETURNS TRIGGER AS $$
 * BEGIN
 * -- Get the product ID
 * DECLARE
 * v_product_id TEXT;
 * BEGIN
 * IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
 * v_product_id := NEW."productId";
 * ELSIF TG_OP = 'DELETE' THEN
 * v_product_id := OLD."productId";
 * END IF;
 * -- Force recalculation by updating the product
 * IF v_product_id IS NOT NULL THEN
 * RAISE NOTICE 'Updating product % due to discount change', v_product_id;
 * UPDATE "Product"
 * SET "id" = "id" -- This is a no-op update that triggers the calculate_final_price function
 * WHERE "id" = v_product_id;
 * END IF;
 * END;
 * RETURN NULL;
 * END;
 * $$ LANGUAGE plpgsql;
 * -- Recreate the discount change triggers
 * DROP TRIGGER IF EXISTS product_discount_insert_trigger ON "ProductDiscount";
 * DROP TRIGGER IF EXISTS product_discount_update_trigger ON "ProductDiscount";
 * DROP TRIGGER IF EXISTS product_discount_delete_trigger ON "ProductDiscount";
 * CREATE TRIGGER product_discount_insert_trigger
 * AFTER INSERT ON "ProductDiscount"
 * FOR EACH ROW
 * EXECUTE FUNCTION update_product_price_on_discount_change();
 * CREATE TRIGGER product_discount_update_trigger
 * AFTER UPDATE ON "ProductDiscount"
 * FOR EACH ROW
 * EXECUTE FUNCTION update_product_price_on_discount_change();
 * CREATE TRIGGER product_discount_delete_trigger
 * AFTER DELETE ON "ProductDiscount"
 * FOR EACH ROW
 * EXECUTE FUNCTION update_product_price_on_discount_change();
 * -- Also update the discount update trigger
 * CREATE OR REPLACE FUNCTION update_products_on_discount_change()
 * RETURNS TRIGGER AS $$
 * BEGIN
 * -- Update all products using this discount to recalculate prices
 * IF TG_OP = 'UPDATE' AND (
 * OLD."value" IS DISTINCT FROM NEW."value" OR
 * OLD."type" IS DISTINCT FROM NEW."type" OR
 * OLD."active" IS DISTINCT FROM NEW."active" OR
 * OLD."startDate" IS DISTINCT FROM NEW."startDate" OR
 * OLD."endDate" IS DISTINCT FROM NEW."endDate"
 * ) THEN
 * RAISE NOTICE 'Discount % changed, updating all associated products', NEW."id";
 * UPDATE "Product" p
 * SET "id" = p."id" -- This is a no-op update that triggers the calculate_final_price function
 * FROM "ProductDiscount" pd
 * WHERE pd."productId" = p."id" AND pd."discountId" = NEW."id";
 * END IF;
 * RETURN NULL;
 * END;
 * $$ LANGUAGE plpgsql;
 * -- Recreate the discount update trigger
 * DROP TRIGGER IF EXISTS discount_update_trigger ON "Discount";
 * CREATE TRIGGER discount_update_trigger
 * AFTER UPDATE ON "Discount"
 * FOR EACH ROW
 * EXECUTE FUNCTION update_products_on_discount_change();
 */
