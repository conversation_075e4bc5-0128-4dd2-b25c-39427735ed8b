//loading component for product/[material_number] route with skeleton based on the page.tsx
import { Skeleton } from "@/components/ui/skeleton"
import { Home } from "lucide-react"

export default function Loading() {
  return (
    <div className="max-w-[1640px] mx-auto px-4 sm:px-6 lg:px-8">
      {/* Breadcrumbs Skeleton */}
      <nav className="text-sm my-8">
        <ol className="flex items-center space-x-2">
          <li>
            <Home className="inline-block w-4 h-4 text-muted-foreground" />
          </li>
          <li>
            <span className="text-muted-foreground">/</span>
          </li>
          <li>
            <Skeleton className="h-4 w-20" />
          </li>
          <li>
            <span className="text-muted-foreground">/</span>
          </li>
          <li>
            <Skeleton className="h-4 w-32" />
          </li>
        </ol>
      </nav>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {/* Left-side Product Image Skeleton */}
        <div className="space-y-4">
          <div className="w-full lg:max-w-xl aspect-w-4 aspect-h-3 bg-gray-100 rounded-lg overflow-hidden">
            <Skeleton className="w-full h-[330px] rounded-lg" />
          </div>

          {/* Thumbnails Skeleton */}
          <div className="flex gap-2 overflow-x-auto">
            {Array.from({ length: 4 }).map((_, index) => (
              <Skeleton key={index} className="w-20 h-20 rounded-md flex-shrink-0" />
            ))}
          </div>
        </div>

        {/* Right-side Product Info Skeleton */}
        <div className="space-y-6">
          {/* Title Skeleton */}
          <div>
            <Skeleton className="h-8 w-3/4 mb-2" />
            <Skeleton className="h-5 w-1/2" />
          </div>
          
          {/* Price Skeleton */}
          <div className="flex items-baseline gap-4">
            <Skeleton className="h-8 w-24" />
            <Skeleton className="h-6 w-20" />
            <Skeleton className="h-6 w-16 rounded-full" />
          </div>

          {/* Stock Status Skeleton */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Skeleton className="w-3 h-3 rounded-full" />
              <Skeleton className="h-4 w-20" />
            </div>
            <Skeleton className="h-4 w-16" />
          </div>

          {/* Product Tabs Skeleton */}
          <div className="w-full mt-6">
            <div className="grid grid-cols-3 mb-4 gap-2">
              <Skeleton className="h-10 rounded-md" />
              <Skeleton className="h-10 rounded-md" />
              <Skeleton className="h-10 rounded-md" />
            </div>

            <div className="p-4 border rounded-md">
              <Skeleton className="h-5 w-32 mb-2" />
              <div className="grid grid-cols-5 gap-2">
                {Array.from({ length: 10 }).map((_, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Skeleton className="w-2 h-2 rounded-full" />
                    <Skeleton className="h-4 w-12" />
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Add to Cart Section Skeleton */}
          <div className="pt-6 border-t border-gray-200">
            <div className="flex items-center gap-4">
              <Skeleton className="flex-1 h-10 rounded-md" />
              <Skeleton className="w-10 h-10 rounded-md" />
            </div>
          </div>
        </div>

        {/* Product Information Skeleton */}
        <div className="p-4 rounded-lg">
          <Skeleton className="h-6 w-24 mb-4" />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="flex justify-between items-center py-2 border-b border-gray-200">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-16" />
              </div>
            ))}
          </div>
        </div>
        
        {/* Product Attributes Skeleton */}
        <div className="p-4 rounded-lg">
          <Skeleton className="h-6 w-20 mb-4" />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="flex justify-between items-center py-2 border-b border-gray-200">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-20" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

// export default function ProductRouteSkeleton() {
//   return (
//     <div className="min-h-screen">  
//       <div className="max-w-[1640px] mx-auto px-4 py-6">
//         <div className="space-y-4">
//           <div className="flex items-center justify-center py-8">
//             <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#0066B1]"></div>
//             <span className="ml-2 text-gray-600">Se incarca...</span>
//           </div>
//         </div>
//       </div></div>  
//       );
// }   