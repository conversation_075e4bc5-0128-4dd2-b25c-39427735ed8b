// app/lib/rateLimiter.ts
export class RateLimiter {
    private requests = new Map<string, { count: number; expiresAt: number }>();
    
    constructor(
      private readonly maxRequests: number,
      private readonly intervalSeconds: number
    ) {}
  
    public checkLimit(identifier: string): boolean {
      const current = this.requests.get(identifier) || { count: 0, expiresAt: 0 };
      
      if (Date.now() > current.expiresAt) {
        this.requests.set(identifier, {
          count: 1,
          expiresAt: Date.now() + this.intervalSeconds * 1000
        });
        return true;
      }
  
      if (current.count >= this.maxRequests) return false;
      
      current.count++;
      return true;
    }
  }