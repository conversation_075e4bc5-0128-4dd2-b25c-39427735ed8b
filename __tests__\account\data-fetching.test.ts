import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { getAccountSettingsData, getUserSecurityInfo, getUserAuditLogs } from '@/app/getData/account-settings';

// Mock dependencies
jest.mock('@/lib/db');
jest.mock('@/lib/logger');

const mockPrismaUser = {
  findUnique: jest.fn(),
};
const mockPrismaUserAuditLog = {
  findMany: jest.fn(),
};
const mockWithRetry = jest.fn();
const mockLogger = {
  warn: jest.fn(),
  error: jest.fn(),
};

beforeEach(() => {
  jest.clearAllMocks();
  
  // Mock database
  require('@/lib/db').prisma = {
    user: mockPrismaUser,
    userAuditLog: mockPrismaUserAuditLog,
  };
  require('@/lib/db').withRetry = mockWithRetry;
  
  // Mock logger
  require('@/lib/logger').logger = mockLogger;

  // Setup default mock implementations
  mockWithRetry.mockImplementation((fn) => fn());
});

describe('Account Data Fetching', () => {
  describe('getAccountSettingsData', () => {
    const mockUserData = {
      id: 'user-123',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      profileImage: 'https://example.com/avatar.jpg',
      phoneNumber: '+***********',
      bio: 'Software developer',
      jobTitle: 'Senior Developer',
      department: 'IT',
      salutation: 'Dl',
      preferredLanguage: 'ro',
      timezone: 'Europe/Bucharest',
      emailNotifications: true,
      pushNotifications: false,
      smsNotifications: true,
      newsletterOptIn: false,
      twoFactorEnabled: true,
      lastLoginAt: new Date('2024-01-01'),
      loginCount: 10,
      passwordChangedAt: new Date('2023-12-01'),
      createdAt: new Date('2023-01-01'),
      updatedAt: new Date('2024-01-01'),
    };

    it('should successfully fetch account settings data', async () => {
      mockPrismaUser.findUnique.mockResolvedValue(mockUserData);

      const result = await getAccountSettingsData('user-123');

      expect(result).toEqual(mockUserData);
      expect(mockPrismaUser.findUnique).toHaveBeenCalledWith({
        where: {
          id: 'user-123',
          isActive: true,
          isSuspended: false,
          deletedAt: null,
        },
        select: expect.objectContaining({
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          // ... other fields
        }),
      });
    });

    it('should return null for non-existent user', async () => {
      mockPrismaUser.findUnique.mockResolvedValue(null);

      const result = await getAccountSettingsData('non-existent-user');

      expect(result).toBeNull();
      expect(mockLogger.warn).toHaveBeenCalledWith(
        expect.stringContaining('User not found or inactive: non-existent-user')
      );
    });

    it('should handle database errors gracefully', async () => {
      mockPrismaUser.findUnique.mockRejectedValue(new Error('Database error'));

      const result = await getAccountSettingsData('user-123');

      expect(result).toBeNull();
      expect(mockLogger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error fetching account data for user user-123'),
        expect.any(Error)
      );
    });

    it('should only fetch active, non-suspended users', async () => {
      await getAccountSettingsData('user-123');

      expect(mockPrismaUser.findUnique).toHaveBeenCalledWith({
        where: {
          id: 'user-123',
          isActive: true,
          isSuspended: false,
          deletedAt: null,
        },
        select: expect.any(Object),
      });
    });
  });

  describe('getUserSecurityInfo', () => {
    const mockSecurityData = {
      loginCount: 25,
      lastLoginAt: new Date('2024-01-01'),
      passwordChangedAt: new Date('2023-12-01'),
      twoFactorEnabled: true,
      loginAttempts: 0,
      lockoutUntil: null,
    };

    it('should successfully fetch security info', async () => {
      mockPrismaUser.findUnique.mockResolvedValue(mockSecurityData);

      const result = await getUserSecurityInfo('user-123');

      expect(result).toEqual(mockSecurityData);
      expect(mockPrismaUser.findUnique).toHaveBeenCalledWith({
        where: {
          id: 'user-123',
          isActive: true,
          isSuspended: false,
          deletedAt: null,
        },
        select: {
          loginCount: true,
          lastLoginAt: true,
          passwordChangedAt: true,
          twoFactorEnabled: true,
          loginAttempts: true,
          lockoutUntil: true,
        },
      });
    });

    it('should return null for non-existent user', async () => {
      mockPrismaUser.findUnique.mockResolvedValue(null);

      const result = await getUserSecurityInfo('non-existent-user');

      expect(result).toBeNull();
    });
  });

  describe('getUserAuditLogs', () => {
    const mockAuditLogs = [
      {
        id: 'log-1',
        action: 'profile.update',
        entityType: 'user',
        entityId: 'user-123',
        details: { field: 'firstName' },
        ipAddress: '127.0.0.1',
        userAgent: 'Mozilla/5.0',
        createdAt: new Date('2024-01-01'),
      },
      {
        id: 'log-2',
        action: 'login',
        entityType: 'session',
        entityId: 'session-456',
        details: {},
        ipAddress: '127.0.0.1',
        userAgent: 'Mozilla/5.0',
        createdAt: new Date('2023-12-31'),
      },
    ];

    it('should successfully fetch audit logs', async () => {
      mockPrismaUserAuditLog.findMany.mockResolvedValue(mockAuditLogs);

      const result = await getUserAuditLogs('user-123', 5);

      expect(result).toEqual(mockAuditLogs);
      expect(mockPrismaUserAuditLog.findMany).toHaveBeenCalledWith({
        where: { userId: 'user-123' },
        select: {
          id: true,
          action: true,
          entityType: true,
          entityId: true,
          details: true,
          ipAddress: true,
          userAgent: true,
          createdAt: true,
        },
        orderBy: { createdAt: 'desc' },
        take: 5,
      });
    });

    it('should use default limit when not specified', async () => {
      mockPrismaUserAuditLog.findMany.mockResolvedValue([]);

      await getUserAuditLogs('user-123');

      expect(mockPrismaUserAuditLog.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          take: 10, // default limit
        })
      );
    });

    it('should return empty array on error', async () => {
      mockPrismaUserAuditLog.findMany.mockRejectedValue(new Error('Database error'));

      const result = await getUserAuditLogs('user-123');

      expect(result).toEqual([]);
      expect(mockLogger.error).toHaveBeenCalled();
    });

    it('should order logs by creation date descending', async () => {
      mockPrismaUserAuditLog.findMany.mockResolvedValue([]);

      await getUserAuditLogs('user-123');

      expect(mockPrismaUserAuditLog.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          orderBy: { createdAt: 'desc' },
        })
      );
    });
  });
});
