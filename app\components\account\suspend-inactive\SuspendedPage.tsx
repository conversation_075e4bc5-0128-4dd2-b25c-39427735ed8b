'use client';

import { useRouter } from 'next/navigation';
import { SignOutButton, useUser } from "@clerk/nextjs";
import { Button } from "@/components/ui/button";
import { formatDate } from '@/lib/utils';

export default function AccountSuspendedPage( { lockoutUntil }: { lockoutUntil: Date | null }) {
  const { user } = useUser();
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-slate-50 flex items-center justify-center px-4 -my-8">
      <div className="max-w-4xl mx-auto text-center">
        {/* Warning Icon */}
        <div className="mb-8">
          <div className="inline-flex items-center justify-center w-24 h-24 bg-red-100 rounded-full mb-6">
            <svg className="w-12 h-12 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
        </div>

        {/* Status Message */}
        <div className="mb-12 space-y-4">
          <div className="bg-red-100 text-red-800 px-4 py-2 text-sm font-semibold mb-4 rounded-full inline-block">
            Cont suspendat
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-700 mb-4">
            Contul tau este suspendat
          </h1>
          <p className="text-xl text-slate-600 max-w-2xl mx-auto leading-relaxed">
            Contul tau a fost suspendat temporar. Acest lucru se poate întampla din motive de securitate sau pentru a preveni abuzuri. Suspendarea va expira la {formatDate(lockoutUntil?.toISOString() ?? '')}.
          </p>
        </div>

        {/* Information Card */}
        <div className="max-w-2xl mx-auto mb-8 shadow-xl border-0 bg-white rounded-lg p-8">
          <div className="space-y-6">
            <div className="flex items-center justify-center gap-3 mb-6">
              <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h3 className="text-xl font-bold text-gray-700">
                Statusul contului
              </h3>
            </div>

            <div className="text-left space-y-4">
              <div className="p-4 bg-red-50 rounded-lg border border-red-200">
                <h4 className="font-semibold text-gray-700 mb-2">
                  Motive posibile pentru suspendarea contului:
                </h4>
                <ul className="text-sm text-slate-600 space-y-1">
                  <li>• Activitate suspecta detectata in cont</li>
                  <li>• Multiple incercari esuate de autentificare</li>
                  <li>• Incalcarea termenilor si conditiilor</li>
                  <li>• Activitate frauduloasa de plata</li>
                  <li>• Verificare de securitate necesara</li>
                </ul>
              </div>

              <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                <h4 className="font-semibold text-gray-700 mb-2">
                  Cum puteti reactiva contul:
                </h4>
                <ul className="text-sm text-slate-600 space-y-1">
                  <li>• Contactati suportul tehnic</li>
                  <li>• Furnizati documente de verificare a identitatii</li>
                  <li>• Revizuiti si acceptati termenii si conditiile noastre</li>
                  <li>• Finalizati procesul de verificare a securitatii</li>
                  <li>• Asteptati revizuirea contului (24-48 de ore)</li>
                </ul>
              </div>

              <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                <h4 className="font-semibold text-gray-700 mb-2">
                  Important:
                </h4>
                <p className="text-sm text-slate-600">
                  Pe durata suspendării, nu vei putea plasa comenzi, accesa informațiile contului sau folosi funcțiile premium. Comenzile existente vor fi procesate în mod normal.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
          <button
            onClick={() => window.location.href = 'mailto:<EMAIL>?subject=Account Suspension Appeal'}
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg font-semibold rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
          >
            📧 Contestați Suspendarea
          </button>

          {user ? (
            <SignOutButton>
              <Button
                variant="outline"
                className="border border-gray-300 text-gray-700 hover:bg-gray-50 px-8 py-3 text-lg font-semibold rounded-lg transition-all duration-300"
              >
                🚪 Iesire din cont
              </Button>
            </SignOutButton>
          ) : (
            <Button
              onClick={() => router.push('/sign-in')}
              variant="outline"
              className="border border-gray-300 text-gray-700 hover:bg-gray-50 px-8 py-3 text-lg font-semibold rounded-lg transition-all duration-300"
            >
              ← Conectare cu alt cont
            </Button>
          )}
        </div>

        {/* Support Information */}
        <div className="bg-white rounded-2xl shadow-xl p-8 max-w-3xl mx-auto">
          <h3 className="text-xl font-bold text-gray-700 mb-6">Echipa de Securitate și Contestații</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-3">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <h4 className="font-semibold text-gray-700 mb-2">Email de Securitate</h4>
              <p className="text-sm text-slate-600"><EMAIL></p>
              <p className="text-xs text-slate-500 mt-1">Raspuns prioritar in 24 de ore</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-3">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h4 className="font-semibold text-gray-700 mb-2">Referinta cazului</h4>
              <p className="text-sm text-slate-600">Includeti adresa de email a contului</p>
              <p className="text-xs text-slate-500 mt-1">Pentru o procesare mai rapida</p>
            </div>
          </div>
        </div>

        {/* Footer Note */}
        <div className="mt-12 text-center">
          <p className="text-sm text-slate-500">
            Aceasta masura de securitate ajuta la protejarea contului dumneavoastra si a platformei noastre.<br />
            Va multumim pentru intelegere si cooperare.
          </p>
        </div>
      </div>
    </div>
  );
}
