import type { <PERSON>ada<PERSON> } from "next";
//import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "../globals.css";
import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";


// const geistSans = Geist({
//   variable: "--font-geist-sans",
//   subsets: ["latin"],
// });

// const geistMono = Geist_Mono({
//   variable: "--font-geist-mono",
//   subsets: ["latin"],
// });

export const metadata: Metadata = {
  title: "Automobile Bavaria : Piese auto BMW originale",
  description: "Piese auto BMW originale",
};


export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {

  return (
    <ClerkProvider>
          {children}
    </ClerkProvider>
  );
}


