

import { Webhook } from 'svix';
import { headers } from 'next/headers';
import { WebhookEvent } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';
import { getClientIp } from '@/lib/utils';
import { prisma, withRetry } from '@/lib/db';
import { logger } from '@/lib/logger';
import { handleUserCreated, handleUserDeleted, handleUserUpdated } from '@/lib/clerk-webhook';
import { webhookSchema } from '@/lib/zod';

const webhookSecret = process.env.CLERK_WEBHOOK_SECRET;

export async function POST(req: Request) {

  if (!webhookSecret) {
    logger.warn('[Clerk Webhook] Missing CLERK_WEBHOOK_SECRET');
    return NextResponse.json({ success: false, error: 'Missing webhook secret' }, { status: 500 });
  }

  const clientIp = getClientIp(req);
  const allowedIps = (process.env.ALLOWED_IPS_FOR_CLERK_WEBHOOK || '').split(',').map(ip => ip.trim());

  if (!clientIp || !allowedIps.includes(clientIp)) {
    logger.warn(`[Clerk Webhook] IP not allowed: ${clientIp}`)
    return NextResponse.json({ status: 'forbidden', reason: 'IP not allowed' }, { status: 403 });
  }

  // Verify the webhook signature
  const headerPayload = await headers();
  const svix_id = headerPayload.get('svix-id');
  const svix_timestamp = headerPayload.get('svix-timestamp');
  const svix_signature = headerPayload.get('svix-signature');

  // If there are no headers, error out
  if (!svix_id || !svix_timestamp || !svix_signature) {
    logger.warn(`[Clerk Webhook] Missing svix headers`)
    return NextResponse.json({ success: false, error: 'Missing svix headers' }, { status: 500 });
  }

  // Get the body
  const rawBody = await req.text();

  // 1. Verify signature
  const wh = new Webhook(webhookSecret);
  let evt: WebhookEvent;

  try {
      // Verify the payload with the headers
    evt = wh.verify(rawBody, {
      'svix-id': svix_id,
      'svix-timestamp': svix_timestamp,
      'svix-signature': svix_signature,
    }) as WebhookEvent;
  }catch (err) {
    logger.error('[Clerk Webhook] Error verifying webhook payload:', err);
    return NextResponse.json({ success: false, error: 'Error verifying webhook payload' }, { status: 500 });
  }

  // 2. Validate with Zod
  try {
    webhookSchema.parse(evt); // or evt.data
  } catch (err) {
    logger.error(`[Clerk Webhook] Invalid webhook payload for IP ${clientIp} and event ${evt.type}:`, err)
   return NextResponse.json({ success: false, error: 'Invalid webhook payload' }, { status: 500 });
  }

  // Handle the webhook
  const eventType = evt.type;
  
  try {

    switch (eventType) {

      case 'user.created': {
        await handleUserCreated(evt, headerPayload);
        break;
      }
      
      case 'user.updated': {
        await handleUserUpdated(evt, headerPayload);
        break;
      }
      
      case 'user.deleted': {
        await handleUserDeleted(evt, headerPayload);
        break;
      }

      default:
        // For any other events, just log them
        await withRetry(() => prisma.userAuditLog.create({
          data: {
            userId: evt.data.id as string,
            entityType: 'clerkWebhook',
            entityId: evt.data.id as string,
            details: JSON.stringify({
              message: 'Unhandled Clerk Webhook event',
              eventType: eventType,
              eventData: evt.data,

            }),
            ipAddress: headerPayload.get('x-forwarded-for') || null,
            userAgent: headerPayload.get('user-agent') || null,
            performedBy: 'clerkWebhook',
            action: `unhandled.${eventType}`
          }
        }))
        logger.warn(`[Clerk Webhook] Unhandled event type: ${eventType}`);
        break;
    }
    
    return NextResponse.json({ success: true }, { status: 200 });
  } catch (error) {
    logger.error(`[Clerk Webhook] Error processing ${eventType}:`, error);
    
    // Log the error to the database
    try {
      await withRetry(() => prisma.userAuditLog.create({
        data: {
          action: `error.${eventType}`,
          entityType: 'clerkWebhook',
          entityId: evt.data.id as string || null,
          details: JSON.stringify({ error, event: evt.data }),
          ipAddress: headerPayload.get('x-forwarded-for') || null,
          userAgent: headerPayload.get('user-agent') || null,
          performedBy: 'clerkWebhook',
        }
      }))
    } catch (logError) {
      logger.error(`[Clerk Webhook] Error logging error to database:`, logError);
    }
    return NextResponse.json({ success: false, error: 'Error processing webhook' }, { status: 500 });
  }
}