"server-only"

import WishlistPage from "@/app/components/wishlist/WishlistPage"
import { getWishlistItems } from "@/app/getData/wishlist"
import { Button } from "@/components/ui/button";
import { getCurrentDbUser } from "@/lib/auth"
import { logger } from "@/lib/logger";
import { getPretSiStocBatch, getPricesFor4thBatch } from "@/lib/mssql/query";
import { GetPrice4LvlBatch } from "@/types/mssql";
import { EnrichedWishlistItem } from "@/types/wishlist";
import { Heart } from "lucide-react";
import Link from "next/link";
import { redirect } from "next/navigation";

export default async function WishlistRoute() {
  try{
    // 1️⃣ Auth
    const user = await getCurrentDbUser();
    if (!user) {
      return redirect("/sign-in");
    }

    // 2️⃣ Load wishlist items
    const wishlistItems = await getWishlistItems(user.id);

    // 3️⃣ Prepare SKU list
    const skus = wishlistItems.map((w) => w.product.Material_Number);

    // 4️⃣ Batch-load stock & conditional 4th-level pricing
    const [pretSiStocMap, price4Batch] = await Promise.all([
      getPretSiStocBatch(skus),
      (user.role.includes("fourLvlAdminAB") ||
        user.role.includes("fourLvlInregistratAB"))
        ? getPricesFor4thBatch(skus, user.userAM || "")
        : Promise.resolve([] as GetPrice4LvlBatch[]),
    ]);

    // 5️⃣ Build price4Map for easy lookup
    const price4Map = new Map(price4Batch.map((p) => [p.itemno, p.pret]));

    // 6️⃣ Merge everything into enriched items
    const enriched: EnrichedWishlistItem[] = wishlistItems.map((item) => {
      const code = item.product.Material_Number;
      const batch = pretSiStocMap[code] ?? [];

      // sum stock across all locations
      const stock = batch.reduce((sum, e) => sum + e.stoc, 0);

      // if user has 4th-level role and price exists, use it; else fall back
      const displayPrice = price4Map.get(code) ?? item.product.FinalPrice;

      return {
        ...item,
        stock,
        displayPrice,
      };
    });

    if(enriched.length === 0){
      return (
            <div className="flex max-w-[1640px] mx-auto p-4 sm:p-6 lg:p-8 flex-col items-center justify-center rounded-lg border border-dashed text-center">
        <div className="flex h-20 w-20 items-center justify-center rounded-full bg-primary/10">
          <Heart className="w-10 h-10 text-primary" />
        </div>
        <h2 className="mt-6 text-xl font-semibold">Lista ta de favorite este goală</h2>
        <p className="mb-8 mt-2 text-center text-sm leading-6 text-muted-foreground max-w-sm mx-auto">
          Nu ai adăugat încă niciun produs la favorite. Răsfoiește catalogul nostru și salvează-ți preferatele.
        </p>
        <Button asChild>
          <Link href="/">Vezi produsele</Link>
        </Button>
      </div>
      )
    }

    // 7️⃣ Render
    return <WishlistPage wishlistItems={enriched} />;
  }catch(e){
    logger.error(`[WishlistRoute] Error fetching wishlist: ${e}`);
    
    return (
      <div className="max-w-[1640px] mx-auto p-4 sm:p-6 lg:p-8">
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Nu s-au putut incarca favoritele.
          </h2>
          <p className="text-gray-600 mb-6 max-w-md mx-auto">
            A aparut o problema la incarcarea favoritelor. Reincarca pagina sau incearca din nou mai tarziu.
          </p>
        </div>
      </div>
    )
  }
}

