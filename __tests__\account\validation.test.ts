import { describe, it, expect } from '@jest/globals';
import { 
  profileUpdateSchema, 
  passwordChangeSchema, 
  notificationPreferencesSchema, 
  securitySettingsSchema 
} from '@/lib/zod';

describe('Account Validation Schemas', () => {
  describe('profileUpdateSchema', () => {
    it('should validate correct profile data', () => {
      const validData = {
        firstName: 'Ion',
        lastName: '<PERSON><PERSON><PERSON>',
        email: '<EMAIL>',
        phoneNumber: '+***********',
        bio: 'Software developer',
        jobTitle: 'Senior Developer',
        department: 'IT',
        salutation: 'Dl' as const,
        preferredLanguage: 'ro',
        timezone: 'Europe/Bucharest',
      };

      const result = profileUpdateSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should reject invalid email', () => {
      const invalidData = {
        firstName: 'Ion',
        lastName: '<PERSON><PERSON><PERSON>',
        email: 'invalid-email',
      };

      const result = profileUpdateSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('email');
      }
    });

    it('should reject empty required fields', () => {
      const invalidData = {
        firstName: '',
        lastName: '',
        email: '<EMAIL>',
      };

      const result = profileUpdateSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues.some(issue => issue.path.includes('firstName'))).toBe(true);
        expect(result.error.issues.some(issue => issue.path.includes('lastName'))).toBe(true);
      }
    });

    it('should validate Romanian phone numbers', () => {
      const validPhones = ['+***********', '0712345678'];
      const invalidPhones = ['123456', '+1234567890', '07123'];

      validPhones.forEach(phone => {
        const result = profileUpdateSchema.safeParse({
          firstName: 'Ion',
          lastName: 'Popescu',
          email: '<EMAIL>',
          phoneNumber: phone,
        });
        expect(result.success).toBe(true);
      });

      invalidPhones.forEach(phone => {
        const result = profileUpdateSchema.safeParse({
          firstName: 'Ion',
          lastName: 'Popescu',
          email: '<EMAIL>',
          phoneNumber: phone,
        });
        expect(result.success).toBe(false);
      });
    });

    it('should validate bio length', () => {
      const longBio = 'a'.repeat(501);
      const result = profileUpdateSchema.safeParse({
        firstName: 'Ion',
        lastName: 'Popescu',
        email: '<EMAIL>',
        bio: longBio,
      });
      expect(result.success).toBe(false);
    });
  });

  describe('passwordChangeSchema', () => {
    it('should validate correct password change data', () => {
      const validData = {
        currentPassword: 'currentPass123!',
        newPassword: 'NewPass123!',
        confirmPassword: 'NewPass123!',
      };

      const result = passwordChangeSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should reject weak passwords', () => {
      const weakPasswords = [
        'weak',
        '12345678',
        'password',
        'Password',
        'Password123',
        'Password!',
      ];

      weakPasswords.forEach(password => {
        const result = passwordChangeSchema.safeParse({
          currentPassword: 'currentPass123!',
          newPassword: password,
          confirmPassword: password,
        });
        expect(result.success).toBe(false);
      });
    });

    it('should reject mismatched passwords', () => {
      const result = passwordChangeSchema.safeParse({
        currentPassword: 'currentPass123!',
        newPassword: 'NewPass123!',
        confirmPassword: 'DifferentPass123!',
      });
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toContain('confirmPassword');
      }
    });

    it('should require all fields', () => {
      const result = passwordChangeSchema.safeParse({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
      expect(result.success).toBe(false);
    });
  });

  describe('notificationPreferencesSchema', () => {
    it('should validate notification preferences', () => {
      const validData = {
        emailNotifications: true,
        pushNotifications: false,
        smsNotifications: true,
        newsletterOptIn: false,
      };

      const result = notificationPreferencesSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should require boolean values', () => {
      const invalidData = {
        emailNotifications: 'true',
        pushNotifications: 1,
        smsNotifications: null,
        newsletterOptIn: undefined,
      };

      const result = notificationPreferencesSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('securitySettingsSchema', () => {
    it('should validate security settings', () => {
      const validData = {
        twoFactorEnabled: true,
      };

      const result = securitySettingsSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should require boolean value for 2FA', () => {
      const invalidData = {
        twoFactorEnabled: 'true',
      };

      const result = securitySettingsSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });
});
