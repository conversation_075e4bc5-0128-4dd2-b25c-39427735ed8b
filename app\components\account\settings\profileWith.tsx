
// "use client";

// import { useState, useTransition } from "react";
// import { useForm } from "react-hook-form";
// import { zodResolver } from "@hookform/resolvers/zod";
// import { Button } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
// import { Label } from "@/components/ui/label";
// import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
// import { Loader2 } from "lucide-react";
// import { ClerkProfileInput, clerkProfileSchema } from "@/lib/zod";
// import { AccountSettingsData } from "@/app/getData/account-settings";
// import { toast } from "sonner";
// import { useUser } from "@clerk/nextjs";
// import ProfileImageUpload from "./ProfileImageUpload";
// import { formatPhoneNumber } from "@/lib/utils";
// import { PhoneNumberResource } from "@clerk/types";
// import { getClerkErrorMessage } from "@/lib/password";

// interface ProfileFormProps {
//   accountData: AccountSettingsData;
//   ssoProvider: string | null;
// }



// export default function ProfileForm({ accountData, ssoProvider }: ProfileFormProps) {
//   const { user } = useUser();
//   const [isPending, startTransition] = useTransition();

    
//   // Form for Clerk-managed fields
//   const clerkForm = useForm<ClerkProfileInput>({
//     resolver: zodResolver(clerkProfileSchema),
//     defaultValues: {
//       firstName: accountData.firstName || user?.firstName || "",
//       lastName: accountData.lastName || user?.lastName || "",
//       email: accountData.email || user?.emailAddresses[0]?.emailAddress || "",
//       phoneNumber: accountData.phoneNumber || user?.phoneNumbers?.[0]?.phoneNumber || ""
//     }
//   });

//   const onSubmitClerkProfile = (data: ClerkProfileInput) => {
//     if (!user) {
//       toast.error("Utilizatorul nu este autentificat");
//       return;
//     }
    
//     startTransition(async () => {
//       try {
//         // ✅ Update basic profile info FIRST
//         await user.update({
//           firstName: data.firstName,
//           lastName: data.lastName,
//         });

//         // Handle phone number changes
//         const currentPhoneNumber =  accountData.phoneNumber || user?.phoneNumbers?.[0]?.phoneNumber || "";
//         const newPhoneNumber = data.phoneNumber ? formatPhoneNumber(data.phoneNumber) : '';
//         const existingPhoneNumbers = user.phoneNumbers || [];
//         const primaryPhone = existingPhoneNumbers[0];

//         let phoneUpdated = false;
//         let emailUpdated = false;

//         if (newPhoneNumber && newPhoneNumber !== currentPhoneNumber) {
//           if (primaryPhone) {
//             // ✅ Remove existing phone number first
//             await primaryPhone.destroy();
//             await user.reload(); // Reload to get updated user object
//           }
          
//           // ✅ Create new phone number
//           const newPhoneObj = await user.createPhoneNumber({ 
//             phoneNumber: newPhoneNumber // ✅ Correct parameter name
//           });
          
//           // ✅ Reload user to get updated phoneNumbers array
//           await user.reload();
          
//           // ✅ Find the newly created phone number object
//           const createdPhone = user.phoneNumbers?.find((phone: PhoneNumberResource) => 
//             phone.id === newPhoneObj?.id
//           );
          
//           if (createdPhone) {
//             // ✅ Send verification SMS with correct strategy
//             await createdPhone.prepareVerification();
//             phoneUpdated = true;
//           }
          
//         } else if (!newPhoneNumber && primaryPhone) {
//           // ✅ Remove phone number if field is empty
//           await primaryPhone.destroy();
//           await user.reload();
//           phoneUpdated = true;
//         }

//         // Handle email separately if changed
//         if (data.email !== user.emailAddresses[0]?.emailAddress) {
//           // ✅ Correct parameter name: emailAddress instead of email
//           await user.createEmailAddress({ email : data.email });
//           emailUpdated = true;
//         }

//         // ✅ Show appropriate success message
//         if (phoneUpdated && emailUpdated) {
//           toast.success("Profilul a fost actualizat! Verificați SMS-ul și email-ul pentru confirmare.");
//         } else if (phoneUpdated) {
//           toast.success("Profilul a fost actualizat! Verificați SMS-ul pentru confirmarea numărului de telefon.");
//         } else if (emailUpdated) {
//           toast.success("Profilul a fost actualizat! Verificați email-ul pentru confirmare.");
//         } else {
//           toast.success("Profilul a fost actualizat cu succes!");
//         }
//       } catch (error: any) {
//         console.error('Clerk profile update error:', error);
//         const errorMessage = getClerkErrorMessage(error);
//         toast.error(errorMessage);
//       }  
//     });
//   };

//   return (
//     <div className="space-y-6">
//       {/* Clerk-managed profile section */}
//       <Card>
//         <CardHeader>
//           <CardTitle>Informații Personale</CardTitle>
//           <CardDescription>
//             Aceste informații sunt gestionate de sistemul de autentificare
//           </CardDescription>
//         </CardHeader>
//         <CardContent>
//       <form onSubmit={clerkForm.handleSubmit(onSubmitClerkProfile)} className="space-y-8">
        
//         {/* --- Main Layout Grid --- */}
//         {/* On medium screens and up, it's a 2-column grid. On small screens, it stacks. */}
//         <div className="grid grid-cols-1 md:grid-cols-3 gap-8">

//           {/* --- Column 1: Profile Image --- */}
//           {/* This div centers the uploader component within its grid cell. */}
//           <div className="md:col-span-1 flex justify-center items-start pt-4">
//             <ProfileImageUpload />
//           </div>

//           {/* --- Column 2: Text Fields --- */}
//           {/* This div spans the other two columns and contains the rest of the fields. */}
//           <div className="md:col-span-2 space-y-4">
//             <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
//               {/* First Name */}
//               <div className="space-y-2">
//                 <Label htmlFor="firstName">Prenume *</Label>
//                 <Input
//                   id="firstName"
//                   {...clerkForm.register("firstName")}
//                   className={clerkForm.formState.errors.firstName ? "border-red-500" : ""}
//                   disabled={isPending}
//                 />
//                 {clerkForm.formState.errors.firstName && (
//                   <p className="text-sm text-red-600">{clerkForm.formState.errors.firstName.message}</p>
//                 )}
//               </div>

//               {/* Last Name */}
//               <div className="space-y-2">
//                 <Label htmlFor="lastName">Nume de familie *</Label>
//                 <Input
//                   id="lastName"
//                   {...clerkForm.register("lastName")}
//                   className={clerkForm.formState.errors.lastName ? "border-red-500" : ""}
//                   disabled={isPending}
//                 />
//                 {clerkForm.formState.errors.lastName && (
//                   <p className="text-sm text-red-600">{clerkForm.formState.errors.lastName.message}</p>
//                 )}
//               </div>
//             </div>

//             {/* Email (Full Width within this column) */}
//             <div className="space-y-2">
//               <Label htmlFor="email">Email *</Label>
//               <Input
//                 id="email"
//                 type="email"
//                 {...clerkForm.register("email")}
//                 className={`${clerkForm.formState.errors.email ? "border-red-500" : ""} ${ssoProvider ? "bg-gray-100 dark:bg-gray-800 cursor-not-allowed" : ""}`}
//                 readOnly={!!ssoProvider}
//                 disabled={!!ssoProvider || isPending}
//               />
//               {ssoProvider && (
//                 <p className="text-sm text-blue-600">
//                   📧 Email-ul este gestionat de {ssoProvider === 'microsoft' ? 'Microsoft' : ssoProvider === 'google' ? 'Google' : ssoProvider}
//                 </p>
//               )}
//               {clerkForm.formState.errors.email && (
//                 <p className="text-sm text-red-600">{clerkForm.formState.errors.email.message}</p>
//               )}
//             </div>

//             {/* Phone Number (Full Width within this column) */}
//             <div className="space-y-2">
//               <Label htmlFor="phoneNumber">Număr de telefon</Label>
//               <Input
//                 id="phoneNumber"
//                 type="tel"
//                 {...clerkForm.register("phoneNumber")}
//                 className={clerkForm.formState.errors.phoneNumber ? "border-red-500" : ""}
//                 disabled={isPending}
//                 placeholder="+40712345678"
//                 onBlur={(e) => {
//                   // Auto-format on blur
//                   const formatted = formatPhoneNumber(e.target.value);
//                   if (formatted !== e.target.value) {
//                     clerkForm.setValue("phoneNumber", formatted, { shouldDirty: true });
//                   }
//                 }}
//               />
//               <p className="text-xs text-gray-500">
//                 Format internațional: +40712345678 sau format local: 0712345678
//               </p>
//               {user?.phoneNumbers?.[0] && user.phoneNumbers[0].verification?.status !== 'verified' && (
//                 <p className="text-xs text-amber-600">
//                   ⚠️ Numărul de telefon nu este verificat. Verificați SMS-urile pentru cod de confirmare.
//                 </p>
//               )}
//               {clerkForm.formState.errors.phoneNumber && (
//                 <p className="text-sm text-red-600">{clerkForm.formState.errors.phoneNumber.message}</p>
//               )}
//             </div>
            
//           </div>
//         </div>
        
//         {/* --- Submit Button --- */}
//         {/* This is outside the grid, at the bottom of the form. */}
//         <div className="flex justify-end pt-4 border-t">
//           <Button 
//             type="submit" 
//             disabled={isPending || !clerkForm.formState.isDirty}
//             className="bg-[#0066B1] hover:bg-[#004d85] text-white"
//           >
//             {isPending ? (
//               <>
//                 <Loader2 className="mr-2 h-4 w-4 animate-spin" />
//                 Se actualizează...
//               </>
//             ) : (
//               "Actualizează informațiile personale"
//             )}
//           </Button>
//         </div>

//       </form>
//         </CardContent>
//       </Card>
//     </div>
//   );
// }


// export default function ProfileForm({ accountData, ssoProvider }: ProfileFormProps) {
//   const [isPending, startTransition] = useTransition();

//   const {
//     register,
//     handleSubmit,
//     setValue,
//     watch,
//     formState: { errors, isDirty }
//   } = useForm<ProfileUpdateInput>({
//     resolver: zodResolver(profileUpdateSchema),
//     defaultValues: {
//       firstName: accountData.firstName,
//       lastName: accountData.lastName,
//       email: accountData.email,
//       phoneNumber: accountData.phoneNumber || "",
//       bio: accountData.bio || "",
//       jobTitle: accountData.jobTitle || "",
//       department: accountData.department || "",
//       salutation: accountData.salutation || undefined,
//       preferredLanguage: accountData.preferredLanguage || "",
//       timezone: accountData.timezone || "",
//     }
//   });

//   const salutationValue = watch("salutation");

//   const onSubmit = (data: ProfileUpdateInput) => {
//     startTransition(async () => {
//       try {
//         const result = await updateProfile(data);
//         if(result.success === true){
//           toast.success("Profilul a fost actualizat cu succes!");
//         } else {
//           toast.error(result.error);
//         }
//       } catch (error) {
//         toast.error("A apărut o eroare neașteptată. Vă rugăm să încercați din nou.");
//       }
//     });
//   };

//   return (
//     <Card>
//       <CardHeader>
//         <CardTitle>Informații Profil</CardTitle>
//         <CardDescription>
//           Actualizați informațiile dvs. personale și de contact
//         </CardDescription>
//       </CardHeader>
//       <CardContent>
//         <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
//           <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
//             {/* Salutation */}
//             <div className="space-y-2">
//               <Label htmlFor="salutation">Titulatura</Label>
//               <Select
//                 value={salutationValue || ""}
//                 onValueChange={(value) => setValue("salutation", value as "Dl" | "Dna", { shouldDirty: true })}
//               >
//                 <SelectTrigger>
//                   <SelectValue placeholder="Selectați titulatura" />
//                 </SelectTrigger>
//                 <SelectContent>
//                   <SelectItem value="Dl">Dl.</SelectItem>
//                   <SelectItem value="Dna">Dna.</SelectItem>
//                 </SelectContent>
//               </Select>
//               {errors.salutation && (
//                 <p className="text-sm text-red-600">{errors.salutation.message}</p>
//               )}
//             </div>

//             {/* First Name */}
//             <div className="space-y-2">
//               <Label htmlFor="firstName">Prenume *</Label>
//               <Input
//                 id="firstName"
//                 {...register("firstName")}
//                 className={errors.firstName ? "border-red-500" : ""}
//               />
//               {errors.firstName && (
//                 <p className="text-sm text-red-600">{errors.firstName.message}</p>
//               )}
//             </div>

//             {/* Last Name */}
//             <div className="space-y-2">
//               <Label htmlFor="lastName">Nume de familie *</Label>
//               <Input
//                 id="lastName"
//                 {...register("lastName")}
//                 className={errors.lastName ? "border-red-500" : ""}
//               />
//               {errors.lastName && (
//                 <p className="text-sm text-red-600">{errors.lastName.message}</p>
//               )}
//             </div>

//             {/* Email */}
//             <div className="space-y-2">
//               <Label htmlFor="email">Email *</Label>
//               <Input
//                 id="email"
//                 type="email"
//                 {...register("email")}
//                 className={`${errors.email ? "border-red-500" : ""} ${ssoProvider ? "bg-gray-50 text-gray-600 cursor-not-allowed" : ""}`}
//                 readOnly={!!ssoProvider}
//                 disabled={!!ssoProvider}
//               />
//               {ssoProvider && (
//                 <p className="text-sm text-blue-600 flex items-center gap-1">
//                   <span>📧</span>
//                   Email-ul este gestionat de {ssoProvider === 'microsoft' ? 'Microsoft' : ssoProvider === 'google' ? 'Google' : ssoProvider}
//                 </p>
//               )}
//               {errors.email && (
//                 <p className="text-sm text-red-600">{errors.email.message}</p>
//               )}
//             </div>

//             {/* Phone Number */}
//             <div className="space-y-2">
//               <Label htmlFor="phoneNumber">Telefon</Label>
//               <Input
//                 id="phoneNumber"
//                 type="tel"
//                 placeholder="+40712345678"
//                 {...register("phoneNumber")}
//                 className={errors.phoneNumber ? "border-red-500" : ""}
//               />
//               {errors.phoneNumber && (
//                 <p className="text-sm text-red-600">{errors.phoneNumber.message}</p>
//               )}
//             </div>

//             {/* Job Title */}
//             <div className="space-y-2">
//               <Label htmlFor="jobTitle">Funcția</Label>
//               <Input
//                 id="jobTitle"
//                 {...register("jobTitle")}
//                 className={errors.jobTitle ? "border-red-500" : ""}
//               />
//               {errors.jobTitle && (
//                 <p className="text-sm text-red-600">{errors.jobTitle.message}</p>
//               )}
//             </div>

//             {/* Department */}
//             <div className="space-y-2">
//               <Label htmlFor="department">Departament</Label>
//               <Input
//                 id="department"
//                 {...register("department")}
//                 className={errors.department ? "border-red-500" : ""}
//               />
//               {errors.department && (
//                 <p className="text-sm text-red-600">{errors.department.message}</p>
//               )}
//             </div>

//             {/* Preferred Language */}
//             <div className="space-y-2">
//               <Label htmlFor="preferredLanguage">Limba preferată</Label>
//               <Select
//                 value={watch("preferredLanguage") || ""}
//                 onValueChange={(value) => setValue("preferredLanguage", value, { shouldDirty: true })}
//               >
//                 <SelectTrigger>
//                   <SelectValue placeholder="Selectați limba" />
//                 </SelectTrigger>
//                 <SelectContent>
//                   <SelectItem value="ro">Română</SelectItem>
//                   <SelectItem value="en">English</SelectItem>
//                   <SelectItem value="de">Deutsch</SelectItem>
//                   <SelectItem value="fr">Français</SelectItem>
//                 </SelectContent>
//               </Select>
//               {errors.preferredLanguage && (
//                 <p className="text-sm text-red-600">{errors.preferredLanguage.message}</p>
//               )}
//             </div>

//             {/* Timezone */}
//             <div className="space-y-2">
//               <Label htmlFor="timezone">Fusul orar</Label>
//               <Select
//                 value={watch("timezone") || ""}
//                 onValueChange={(value) => setValue("timezone", value, { shouldDirty: true })}
//               >
//                 <SelectTrigger>
//                   <SelectValue placeholder="Selectați fusul orar" />
//                 </SelectTrigger>
//                 <SelectContent>
//                   <SelectItem value="Europe/Bucharest">Europa/București</SelectItem>
//                   <SelectItem value="Europe/London">Europa/Londra</SelectItem>
//                   <SelectItem value="Europe/Berlin">Europa/Berlin</SelectItem>
//                   <SelectItem value="America/New_York">America/New York</SelectItem>
//                 </SelectContent>
//               </Select>
//               {errors.timezone && (
//                 <p className="text-sm text-red-600">{errors.timezone.message}</p>
//               )}
//             </div>
//           </div>

//           {/* Bio - Full Width */}
//           <div className="space-y-2">
//             <Label htmlFor="bio">Biografie</Label>
//             <Textarea
//               id="bio"
//               placeholder="Scrieți câteva cuvinte despre dvs..."
//               rows={4}
//               {...register("bio")}
//               className={errors.bio ? "border-red-500" : ""}
//             />
//             {errors.bio && (
//               <p className="text-sm text-red-600">{errors.bio.message}</p>
//             )}
//           </div>

//           {/* Submit Button */}
//           <div className="flex justify-end">
//             <Button 
//               type="submit" 
//               disabled={isPending || !isDirty}
//               className="bg-[#0066B1] hover:bg-[#004d85] text-white"
//             >
//               {isPending ? (
//                 <>
//                   <Loader2 className="mr-2 h-4 w-4 animate-spin" />
//                   Se salvează...
//                 </>
//               ) : (
//                 "Salvează modificările"
//               )}
//             </Button>
//           </div>
//         </form>
//       </CardContent>
//     </Card>
//   );
// }





