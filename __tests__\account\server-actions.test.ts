import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { updateProfile, updateNotificationPreferences, updateSecuritySettings } from '@/app/actions/account';

// Mock dependencies
jest.mock('@/lib/auth');
jest.mock('@/lib/db');
jest.mock('@/lib/logger');
jest.mock('next/headers');
jest.mock('@clerk/nextjs/server');

const mockGetCurrentDbUser = jest.fn();
const mockPrismaUser = {
  update: jest.fn(),
};
const mockPrismaUserAuditLog = {
  create: jest.fn(),
};
const mockWithRetry = jest.fn();
const mockHeaders = jest.fn();

// Setup mocks
beforeEach(() => {
  jest.clearAllMocks();
  
  // Mock auth
  require('@/lib/auth').getCurrentDbUser = mockGetCurrentDbUser;
  
  // Mock database
  require('@/lib/db').prisma = {
    user: mockPrismaUser,
    userAuditLog: mockPrismaUserAuditLog,
  };
  require('@/lib/db').withRetry = mockWithRetry;
  
  // Mock headers
  require('next/headers').headers = mockHeaders;
  
  // Mock Clerk
  require('@clerk/nextjs/server').currentUser = jest.fn();
  require('@clerk/nextjs/server').clerkClient = {
    users: {
      updateUser: jest.fn(),
    },
  };

  // Setup default mock implementations
  mockWithRetry.mockImplementation((fn) => fn());
  mockHeaders.mockResolvedValue(new Map([
    ['x-forwarded-for', '127.0.0.1'],
    ['user-agent', 'test-agent'],
  ]));
});

describe('Account Server Actions', () => {
  describe('updateProfile', () => {
    const mockUser = {
      id: 'user-123',
      email: '<EMAIL>',
      firstName: 'Old',
      lastName: 'Name',
      phoneNumber: null,
      bio: null,
      jobTitle: null,
      department: null,
      salutation: null,
      preferredLanguage: null,
      timezone: null,
    };

    beforeEach(() => {
      mockGetCurrentDbUser.mockResolvedValue(mockUser);
      mockPrismaUser.update.mockResolvedValue({ ...mockUser, firstName: 'New' });
      mockPrismaUserAuditLog.create.mockResolvedValue({});
    });

    it('should successfully update profile', async () => {
      const input = {
        firstName: 'New',
        lastName: 'Name',
        email: '<EMAIL>',
        phoneNumber: '+***********',
        bio: 'Updated bio',
        jobTitle: 'Developer',
        department: 'IT',
        salutation: 'Dl' as const,
        preferredLanguage: 'ro',
        timezone: 'Europe/Bucharest',
      };

      const result = await updateProfile(input);

      expect(result.success).toBe(true);
      expect(mockPrismaUser.update).toHaveBeenCalledWith({
        where: { id: 'user-123' },
        data: expect.objectContaining({
          firstName: 'New',
          lastName: 'Name',
          email: '<EMAIL>',
        }),
      });
      expect(mockPrismaUserAuditLog.create).toHaveBeenCalled();
    });

    it('should return error when user not authenticated', async () => {
      mockGetCurrentDbUser.mockResolvedValue(null);

      const input = {
        firstName: 'New',
        lastName: 'Name',
        email: '<EMAIL>',
      };

      const result = await updateProfile(input);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Utilizatorul nu este autentificat');
    });

    it('should return validation errors for invalid input', async () => {
      const input = {
        firstName: '', // Invalid: empty
        lastName: 'Name',
        email: 'invalid-email', // Invalid: not an email
      };

      const result = await updateProfile(input);

      expect(result.success).toBe(false);
      expect(result.fieldErrors).toBeDefined();
    });

    it('should check for duplicate email', async () => {
      // Mock finding existing user with same email
      require('@/lib/db').prisma.user.findUnique = jest.fn().mockResolvedValue({
        id: 'other-user',
        email: '<EMAIL>',
      });

      const input = {
        firstName: 'New',
        lastName: 'Name',
        email: '<EMAIL>',
      };

      const result = await updateProfile(input);

      expect(result.success).toBe(false);
      expect(result.error).toContain('email este deja folosită');
    });

    it('should handle database errors gracefully', async () => {
      mockPrismaUser.update.mockRejectedValue(new Error('Database error'));

      const input = {
        firstName: 'New',
        lastName: 'Name',
        email: '<EMAIL>',
      };

      const result = await updateProfile(input);

      expect(result.success).toBe(false);
      expect(result.error).toContain('eroare la actualizarea profilului');
    });
  });

  describe('updateNotificationPreferences', () => {
    const mockUser = {
      id: 'user-123',
      emailNotifications: true,
      pushNotifications: false,
      smsNotifications: false,
      newsletterOptIn: true,
    };

    beforeEach(() => {
      mockGetCurrentDbUser.mockResolvedValue(mockUser);
      mockPrismaUser.update.mockResolvedValue(mockUser);
      mockPrismaUserAuditLog.create.mockResolvedValue({});
    });

    it('should successfully update notification preferences', async () => {
      const input = {
        emailNotifications: false,
        pushNotifications: true,
        smsNotifications: true,
        newsletterOptIn: false,
      };

      const result = await updateNotificationPreferences(input);

      expect(result.success).toBe(true);
      expect(mockPrismaUser.update).toHaveBeenCalledWith({
        where: { id: 'user-123' },
        data: expect.objectContaining(input),
      });
      expect(mockPrismaUserAuditLog.create).toHaveBeenCalled();
    });

    it('should return error when user not authenticated', async () => {
      mockGetCurrentDbUser.mockResolvedValue(null);

      const input = {
        emailNotifications: true,
        pushNotifications: false,
        smsNotifications: false,
        newsletterOptIn: true,
      };

      const result = await updateNotificationPreferences(input);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Utilizatorul nu este autentificat');
    });
  });

  describe('updateSecuritySettings', () => {
    const mockUser = {
      id: 'user-123',
      twoFactorEnabled: false,
    };

    beforeEach(() => {
      mockGetCurrentDbUser.mockResolvedValue(mockUser);
      mockPrismaUser.update.mockResolvedValue({ ...mockUser, twoFactorEnabled: true });
      mockPrismaUserAuditLog.create.mockResolvedValue({});
    });

    it('should successfully update security settings', async () => {
      const input = {
        twoFactorEnabled: true,
      };

      const result = await updateSecuritySettings(input);

      expect(result.success).toBe(true);
      expect(mockPrismaUser.update).toHaveBeenCalledWith({
        where: { id: 'user-123' },
        data: expect.objectContaining({
          twoFactorEnabled: true,
        }),
      });
      expect(mockPrismaUserAuditLog.create).toHaveBeenCalled();
    });

    it('should return error when user not authenticated', async () => {
      mockGetCurrentDbUser.mockResolvedValue(null);

      const input = {
        twoFactorEnabled: true,
      };

      const result = await updateSecuritySettings(input);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Utilizatorul nu este autentificat');
    });
  });
});
