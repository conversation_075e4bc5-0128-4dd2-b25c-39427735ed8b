'use client';

import { useState } from 'react';
import { useUser, useClerk } from '@clerk/nextjs';
import { toast } from 'sonner';
import { Alert<PERSON>riangle, Loader2, Trash2 } from 'lucide-react';

// Shadcn/UI Component Imports
import { Card, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';

// Update your server action to accept the feedback payload
import { banUser } from '@/app/actions/account';

const DELETION_CONFIRMATION_TEXT = 'STERGE CONTUL MEU';

// To make the checkboxes easier to manage
const CONFIRMATION_CHECKS = [
  { id: 'understand', label: 'Înțeleg că această acțiune este permanentă și ireversibilă.' },
  { id: 'dataLoss', label: 'Accept că toate datele mele vor fi șterse definitiv.' },
  { id: 'noRecovery', label: 'Confirm că nu există nicio modalitate de a-mi recupera contul.' },
];

export default function DeleteAccount() {
  const { user, isLoaded } = useUser();
  const { signOut } = useClerk();

  // Modal and Deletion State
  const [isDeleting, setIsDeleting] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  
  // Multi-step flow state
  const [showFinalConfirmation, setShowFinalConfirmation] = useState(false);
  
  // Form State
  const [reason, setReason] = useState('');
  const [confirmationText, setConfirmationText] = useState('');
  const [confirmations, setConfirmations] = useState({
    understand: false,
    dataLoss: false,
    noRecovery: false
  });

  // Derived state to control button enablement
  const canProceedToFinalStep = Object.values(confirmations).every(Boolean) && reason.trim().length > 0;
  const canDelete = canProceedToFinalStep && confirmationText === DELETION_CONFIRMATION_TEXT;

  // Reset all modal state when it's closed to ensure a clean slate
  const resetModalState = () => {
    setIsDeleting(false);
    setShowFinalConfirmation(false);
    setReason('');
    setConfirmationText('');
    setConfirmations({ understand: false, dataLoss: false, noRecovery: false });
  };

  const handleOpenChange = (open: boolean) => {
    setIsDialogOpen(open);
    if (!open) {
      // Use a timeout to avoid seeing the state reset before the closing animation finishes
      setTimeout(resetModalState, 300);
    }
  };

  const handleDeleteAccount = async () => {
    if (!user || !canDelete) return;

    setIsDeleting(true);
    
    try {
      // Pass the collected feedback to the server action
      const result = await banUser({ reason: reason.trim() });
      
      if (!result.success) {
        toast.error('Nu s-a putut șterge contul. Încercați din nou.');
      } else {
        toast.success('Contul a fost șters cu succes. Ne pare rău să te vedem plecând.');
        // Sign out is crucial. Clerk handles the redirect.
        await signOut();
      }
    } catch (error) {
      console.error("Account deletion error:", error);
      toast.error('A apărut o eroare neașteptată. Vă rugăm să încercați din nou.');
    } finally {
      // No need to set isDeleting to false here if signOut is successful,
      // as the component will unmount. But it's good practice for the error case.
      setIsDeleting(false);
    }
  };

  if (!isLoaded) {
    return (
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div className="space-y-1.5">
              <CardTitle className="flex items-center gap-2 text-red-600">
                <AlertTriangle className="h-5 w-5" />
                Șterge Cont
              </CardTitle>
              <CardDescription>Se încarcă...</CardDescription>
            </div>
            <Button variant="destructive" disabled>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Se încarcă...
            </Button>
          </div>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card className="border-red-500/50">
      <CardHeader>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="space-y-1.5">
            <CardTitle className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              Șterge Contul
            </CardTitle>
            <CardDescription>
              Această acțiune este permanentă și va șterge contul și toate datele asociate.
            </CardDescription>
          </div>
          
          <AlertDialog open={isDialogOpen} onOpenChange={handleOpenChange}>
            <AlertDialogTrigger asChild>
              <Button variant="destructive" className="text-white w-full sm:w-auto">
                <Trash2 className="mr-2 h-4 w-4" />
                Șterge Cont
              </Button>
            </AlertDialogTrigger>
            
            <AlertDialogContent className="max-w-lg">
              {!showFinalConfirmation ? (
                // STEP 1: INFORMATION AND INITIAL CONFIRMATION
                <>
                  <AlertDialogHeader>
                    <AlertDialogTitle className="text-xl">
                      Ești absolut sigur?
                    </AlertDialogTitle>
                    <Alert variant="destructive" className="mt-4">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        <strong>ATENȚIE:</strong> Urmează să ștergi definitiv contul. Această acțiune nu poate fi anulată.
                      </AlertDescription>
                    </Alert>
                  </AlertDialogHeader>

                  <div className="space-y-4 py-2">
                    <div className="space-y-2">
                      <Label htmlFor="reason">Motivul plecării (obligatoriu)</Label>
                      <Textarea
                        id="reason"
                        placeholder="Feedback-ul tău ne ajută să ne îmbunătățim..."
                        value={reason}
                        onChange={(e) => setReason(e.target.value)}
                        rows={3}
                      />
                    </div>
                    <div className="space-y-3">
                      <Label>Te rog, confirmă următoarele:</Label>
                      {CONFIRMATION_CHECKS.map(check => (
                         <div key={check.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={check.id}
                              checked={confirmations[check.id as keyof typeof confirmations]}
                              onCheckedChange={(checked) => 
                                setConfirmations(prev => ({ ...prev, [check.id]: !!checked }))
                              }
                            />
                            <Label htmlFor={check.id} className="text-sm font-normal cursor-pointer">
                              {check.label}
                            </Label>
                          </div>
                      ))}
                    </div>
                  </div>

                  <AlertDialogFooter>
                    <Button variant="outline" onClick={() => handleOpenChange(false)}>Anulează</Button>
                    <Button
                      variant="destructive"
                      onClick={() => setShowFinalConfirmation(true)}
                      disabled={!canProceedToFinalStep}
                    >
                      Continuă
                    </Button>
                  </AlertDialogFooter>
                </>
              ) : (
                // STEP 2: FINAL CONFIRMATION
                <>
                  <AlertDialogHeader>
                    <AlertDialogTitle className="text-xl text-red-600">
                      Confirmare Finală
                    </AlertDialogTitle>
                     <Alert variant="destructive" className="mt-4">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        Aceasta este ultima șansă. Pentru a șterge definitiv contul, tastează textul de mai jos.
                      </AlertDescription>
                    </Alert>
                  </AlertDialogHeader>

                  <div className="space-y-2 py-4">
                    <Label htmlFor="confirmation" className="font-semibold">
                      Tastează <strong className="text-red-600">{DELETION_CONFIRMATION_TEXT}</strong> pentru a confirma.
                    </Label>
                    <Input
                      id="confirmation"
                      value={confirmationText}
                      onChange={(e) => setConfirmationText(e.target.value)}
                      placeholder={DELETION_CONFIRMATION_TEXT}
                      autoComplete="off"
                    />
                  </div>
                  
                  <AlertDialogFooter>
                    <Button variant="outline" onClick={() => setShowFinalConfirmation(false)} disabled={isDeleting}>
                      Înapoi
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={handleDeleteAccount}
                      disabled={isDeleting || !canDelete}
                      className="text-white"
                    >
                      {isDeleting ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          Se șterge...
                        </>
                      ) : (
                        'Șterge Definitiv Contul'
                      )}
                    </Button>
                  </AlertDialogFooter>
                </>
              )}
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </CardHeader>
    </Card>
  );
}




// import { useState } from 'react';
// import { useUser, useClerk } from '@clerk/nextjs';
// import { AlertTriangle, Loader2 } from 'lucide-react';
// import {
//   Card,
//   CardHeader,
//   CardTitle,
//   CardDescription,
// } from '@/components/ui/card';
// import { Button } from '@/components/ui/button';
// import {
//   AlertDialog,
//   AlertDialogAction,
//   AlertDialogCancel,
//   AlertDialogContent,
//   AlertDialogDescription,
//   AlertDialogFooter,
//   AlertDialogHeader,
//   AlertDialogTitle,
//   AlertDialogTrigger,
// } from '@/components/ui/alert-dialog';
// import { toast } from 'sonner';
// import { banUser } from '@/app/actions/account';


// export default function DeleteAccount() {
//   const { user, isLoaded } = useUser();
//   const { signOut } = useClerk();
//   const [isLocking, setIsLocking] = useState(false);
//   const [isDialogOpen, setIsDialogOpen] = useState(false);

//   const handleLockAccount = async () => {
//     if (!user) return;

//     setIsLocking(true);
    
//     try {
//       const result = await banUser();
//       if (!result.success) {
//         toast.error("Nu s-a putut sterge contul.");
//         return;
//       } else {
//         toast.success("Contul a fost sters cu succes.");
//       }

//       Sign out the user after successful lock
//       await signOut();
      
//     } catch (error) {
//       toast.error("A aparut o eroare neașteptată. Va rugam sa incercati din nou.");
        
//     } finally {
//       setIsLocking(false);
//       setIsDialogOpen(false);
//     }
//   };

//   Don't render if user data isn't loaded yet
//   if (!isLoaded) {
//     return (
//       <Card>
//         <CardHeader>
//           <div className="flex justify-between items-center">
//             <div className="space-y-2">
//               <CardTitle className="flex items-center gap-2 text-red-600">
//                 <AlertTriangle className="h-5 w-5" />
//                 Sterge Cont
//               </CardTitle>
//               <CardDescription>
//                  Se incarca...
//               </CardDescription>
//             </div>
//             <Button variant="destructive" disabled>
//               <Loader2 className="h-4 w-4 animate-spin mr-2" />
//               Sterge Cont
//             </Button>
//           </div>
//         </CardHeader>
//       </Card>
//     );
//   }

//   return (
//     <Card>
//       <CardHeader>
//         <div className="flex justify-between items-center">
//           <div className="space-y-2">
//             <CardTitle className="flex items-center gap-2 text-red-600">
//               <AlertTriangle className="h-5 w-5" />
//               Sterge Cont
//             </CardTitle>
//             <CardDescription>
//               Sterge contul si toate datele asociate.
//             </CardDescription>
//           </div>
          
//           <AlertDialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
//             <AlertDialogTrigger asChild>
//               <Button 
//                 variant="destructive" 
//                 className="text-white"
//                 disabled={isLocking || !user}
//               >
//                 {isLocking ? (
//                   <>
//                     <Loader2 className="h-4 w-4 animate-spin mr-2" />
//                     Sterge Cont...
//                   </>
//                 ) : (
//                   'Sterge Cont'
//                 )}
//               </Button>
//             </AlertDialogTrigger>
            
//             <AlertDialogContent>
//               <AlertDialogHeader>
//                 <AlertDialogTitle className="flex items-center gap-2 text-red-600">
//                   <AlertTriangle className="h-5 w-5" />
//                   Sterge Cont
//                 </AlertDialogTitle>
//                 <AlertDialogDescription className="space-y-2">
//                   <span>
//                     <strong>Aceasta actiune este ireversibila.</strong> Va fi sters contul tau si toate datele asociate.
//                     Va fi notificat daca contul tau este sters.
//                     Sunteti sigur ca doriti sa stergeti contul?
//                   </span>
//                 </AlertDialogDescription>
//               </AlertDialogHeader>
              
//               <AlertDialogFooter>
//                 <AlertDialogCancel disabled={isLocking}>
//                   Anuleaza
//                 </AlertDialogCancel>
//                 <AlertDialogAction
//                   onClick={handleLockAccount}
//                   disabled={isLocking}
//                   className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
//                 >
//                   {isLocking ? (
//                     <>
//                       <Loader2 className="h-4 w-4 animate-spin mr-2" />
//                       Sterge Cont...
//                     </>
//                   ) : (
//                     'Sterge Cont'
//                   )}
//                 </AlertDialogAction>
//               </AlertDialogFooter>
//             </AlertDialogContent>
//           </AlertDialog>
//         </div>
//       </CardHeader>
//     </Card>
//   );
// }